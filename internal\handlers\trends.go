package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/models"
	"go_lilybot_api_trends/internal/services"

	"github.com/gin-gonic/gin"
)

// TrendsHandler handles HTTP requests for trends data
type TrendsHandler struct {
	serpApiService *services.SerpApiService
	analyzer       *services.TrendsAnalyzer
	config         *config.Config
}

// NewTrendsHandler creates a new trends handler
func NewTrendsHandler(serpApiService *services.SerpApiService, analyzer *services.TrendsAnalyzer, cfg *config.Config) *TrendsHandler {
	return &TrendsHandler{
		serpApiService: serpApiService,
		analyzer:       analyzer,
		config:         cfg,
	}
}

// GetTrends handles general trends requests
// @Summary Get Google Trends data
// @Description Fetch trends data from Google Trends via SerpApi
// @Tags trends
// @Accept json
// @Produce json
// @Param query query string true "Search query (comma-separated for multiple)"
// @Param data_type query string false "Data type (TIMESERIES, GEO_MAP_0, RELATED_TOPICS, RELATED_QUERIES, GEO_MAP)" default(TIMESERIES)
// @Param geo query string false "Geographic location code"
// @Param date query string false "Date range" default(today 12-m)
// @Param language query string false "Language code" default(en)
// @Param analyze query bool false "Include analysis" default(false)
// @Success 200 {object} models.TrendsResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/trends [get]
func (h *TrendsHandler) GetTrends(c *gin.Context) {
	// Parse query parameters (no config defaults - use hardcoded fallbacks)
	req := &models.TrendsRequest{
		Query:                  c.Query("query"),
		DataType:               c.DefaultQuery("data_type", "TIMESERIES"),
		Geo:                    c.DefaultQuery("geo", ""),
		Date:                   c.DefaultQuery("date", "now 7-d"),
		Language:               c.DefaultQuery("language", ""),
		Category:               c.DefaultQuery("category", ""),
		Property:               c.Query("property"),
		Timezone:               c.DefaultQuery("timezone", "420"),
		IncludeLowSearchVolume: c.Query("include_low_search_volume") == "true",
	}

	// Validate required parameters
	if req.Query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "query parameter is required"})
		return
	}

	// Validate data type
	if !h.serpApiService.ValidateDataType(req.DataType) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid data_type"})
		return
	}

	// Validate timezone if provided
	if req.Timezone != "" && !h.serpApiService.ValidateTimezone(req.Timezone) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid timezone"})
		return
	}

	// Fetch trends data
	data, err := h.serpApiService.GetTrends(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Check if analysis is requested
	analyze, _ := strconv.ParseBool(c.DefaultQuery("analyze", "false"))
	if analyze {
		analysis := h.analyzer.AnalyzeTrends(data, req.Query)
		c.JSON(http.StatusOK, gin.H{
			"data":     data,
			"analysis": analysis,
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetInterestOverTime handles interest over time requests
// @Summary Get interest over time data
// @Description Fetch interest over time trends data
// @Tags trends
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param geo query string false "Geographic location code"
// @Param date query string false "Date range" default(today 12-m)
// @Param analyze query bool false "Include analysis" default(false)
// @Success 200 {object} models.TrendsResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/trends/interest-over-time [get]
func (h *TrendsHandler) GetInterestOverTime(c *gin.Context) {
	query := c.Query("query")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "query parameter is required"})
		return
	}

	geo := c.DefaultQuery("geo", "")
	date := c.DefaultQuery("date", "now 7-d")

	data, err := h.serpApiService.GetInterestOverTime(query, geo, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	analyze, _ := strconv.ParseBool(c.DefaultQuery("analyze", "false"))
	if analyze {
		analysis := h.analyzer.AnalyzeTrends(data, query)
		c.JSON(http.StatusOK, gin.H{
			"data":     data,
			"analysis": analysis,
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetInterestByRegion handles interest by region requests
// @Summary Get interest by region data
// @Description Fetch interest by region trends data
// @Tags trends
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param geo query string false "Geographic location code"
// @Param date query string false "Date range" default(today 12-m)
// @Param analyze query bool false "Include analysis" default(false)
// @Success 200 {object} models.TrendsResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/trends/interest-by-region [get]
func (h *TrendsHandler) GetInterestByRegion(c *gin.Context) {
	query := c.Query("query")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "query parameter is required"})
		return
	}

	geo := c.DefaultQuery("geo", "")
	date := c.DefaultQuery("date", "now 7-d")

	data, err := h.serpApiService.GetInterestByRegion(query, geo, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	analyze, _ := strconv.ParseBool(c.DefaultQuery("analyze", "false"))
	if analyze {
		analysis := h.analyzer.AnalyzeTrends(data, query)
		c.JSON(http.StatusOK, gin.H{
			"data":     data,
			"analysis": analysis,
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetRelatedTopics handles related topics requests
// @Summary Get related topics data
// @Description Fetch related topics for a search query
// @Tags trends
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param geo query string false "Geographic location code"
// @Param date query string false "Date range" default(today 12-m)
// @Param analyze query bool false "Include analysis" default(false)
// @Success 200 {object} models.TrendsResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/trends/related-topics [get]
func (h *TrendsHandler) GetRelatedTopics(c *gin.Context) {
	query := c.Query("query")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "query parameter is required"})
		return
	}

	geo := c.DefaultQuery("geo", "")
	date := c.DefaultQuery("date", "now 7-d")

	data, err := h.serpApiService.GetRelatedTopics(query, geo, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	analyze, _ := strconv.ParseBool(c.DefaultQuery("analyze", "false"))
	if analyze {
		analysis := h.analyzer.AnalyzeTrends(data, query)
		c.JSON(http.StatusOK, gin.H{
			"data":     data,
			"analysis": analysis,
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetRelatedQueries handles related queries requests
// @Summary Get related queries data
// @Description Fetch related queries for a search query
// @Tags trends
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param geo query string false "Geographic location code"
// @Param date query string false "Date range" default(today 12-m)
// @Param analyze query bool false "Include analysis" default(false)
// @Success 200 {object} models.TrendsResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/trends/related-queries [get]
func (h *TrendsHandler) GetRelatedQueries(c *gin.Context) {
	query := c.Query("query")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "query parameter is required"})
		return
	}

	geo := c.DefaultQuery("geo", "")
	date := c.DefaultQuery("date", "now 7-d")

	data, err := h.serpApiService.GetRelatedQueries(query, geo, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	analyze, _ := strconv.ParseBool(c.DefaultQuery("analyze", "false"))
	if analyze {
		analysis := h.analyzer.AnalyzeTrends(data, query)
		c.JSON(http.StatusOK, gin.H{
			"data":     data,
			"analysis": analysis,
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// CompareQueries handles query comparison requests
// @Summary Compare multiple queries
// @Description Compare trends for multiple search queries
// @Tags trends
// @Accept json
// @Produce json
// @Param queries query string true "Comma-separated search queries (2-5 queries)"
// @Param geo query string false "Geographic location code"
// @Param date query string false "Date range" default(today 12-m)
// @Param analyze query bool false "Include analysis" default(false)
// @Success 200 {object} models.TrendsResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/trends/compare [get]
func (h *TrendsHandler) CompareQueries(c *gin.Context) {
	queriesStr := c.Query("queries")
	if queriesStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "queries parameter is required"})
		return
	}

	queries := strings.Split(queriesStr, ",")
	for i, q := range queries {
		queries[i] = strings.TrimSpace(q)
	}

	if len(queries) < 2 || len(queries) > 5 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "queries must contain 2-5 items"})
		return
	}

	geo := c.DefaultQuery("geo", "")
	date := c.DefaultQuery("date", "now 7-d")

	data, err := h.serpApiService.CompareQueries(queries, geo, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	analyze, _ := strconv.ParseBool(c.DefaultQuery("analyze", "false"))
	if analyze {
		analysis := h.analyzer.AnalyzeTrends(data, queriesStr)
		c.JSON(http.StatusOK, gin.H{
			"data":     data,
			"analysis": analysis,
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetTrendingNow gets trending searches without specifying keywords
// @Summary Get trending searches
// @Description Get current trending searches from Google Trends (like the explore page)
// @Tags trends
// @Accept json
// @Produce json
// @Param geo query string false "Geographic location code" default(CN)
// @Param hours query int false "Number of past hours (4, 24, 48, 168)" default(168)
// @Param category_id query string false "Category ID to filter results"
// @Param only_active query bool false "Only show active trending searches" default(false)
// @Success 200 {object} models.TrendingNowResponse
// @Failure 500 {object} gin.H
// @Router /api/trends/trending-now [get]
func (h *TrendsHandler) GetTrendingNow(c *gin.Context) {
	geo := c.DefaultQuery("geo", "US")
	hours, _ := strconv.Atoi(c.DefaultQuery("hours", "168"))
	categoryID := c.Query("category_id")
	onlyActive, _ := strconv.ParseBool(c.DefaultQuery("only_active", "false"))

	data, err := h.serpApiService.GetTrendingNow(geo, hours, categoryID, onlyActive)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetTrendsExplore gets Google Trends explore data (like the explore page without specific keywords)
// @Summary Get Google Trends explore data
// @Description Get Google Trends explore data without specifying keywords (like the explore page)
// @Tags trends
// @Accept json
// @Produce json
// @Param geo query string false "Geographic location code" default(CN)
// @Param hl query string false "Language code" default(zh-cn)
// @Param tz query string false "Timezone offset" default(-480)
// @Param region query string false "Region type" default(COUNTRY)
// @Param cat query string false "Category ID (0 for all categories)" default(0)
// @Param data_type query string false "Data type for explore" default(RELATED_TOPICS)
// @Success 200 {object} models.TrendsResponse
// @Failure 500 {object} gin.H
// @Router /api/trends/explore [get]
func (h *TrendsHandler) GetTrendsExplore(c *gin.Context) {
	geo := c.DefaultQuery("geo", "")
	language := c.DefaultQuery("hl", "")
	timezone := c.DefaultQuery("tz", "420")
	region := c.DefaultQuery("region", "")
	category := c.DefaultQuery("cat", "")
	dataType := c.DefaultQuery("data_type", "RELATED_TOPICS")
	date := c.DefaultQuery("date", "now 7-d")

	data, err := h.serpApiService.GetTrendsExplore(geo, language, timezone, region, category, dataType, date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, data)
}
