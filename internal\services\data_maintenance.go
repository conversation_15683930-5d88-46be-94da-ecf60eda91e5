package services

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/config"

	"github.com/robfig/cron/v3"
)

// DataMaintenanceService handles automatic data cleanup
type DataMaintenanceService struct {
	cron     *cron.Cron
	config   *config.DataMaintenanceConfig
	database *DatabaseService
}

// NewDataMaintenanceService creates a new data maintenance service
func NewDataMaintenanceService(cfg *config.DataMaintenanceConfig, database *DatabaseService) *DataMaintenanceService {
	if !cfg.Enabled {
		log.Println("🧹 Data maintenance is disabled")
		return &DataMaintenanceService{
			config:   cfg,
			database: database,
		}
	}

	// Create cron with timezone support (use system timezone for data maintenance)
	location := time.Local

	return &DataMaintenanceService{
		cron:     cron.New(cron.WithLocation(location)),
		config:   cfg,
		database: database,
	}
}

// Start starts the data maintenance scheduler
func (dm *DataMaintenanceService) Start() error {
	if !dm.config.Enabled {
		log.Println("🧹 Data maintenance is disabled, skipping scheduler start")
		return nil
	}

	if dm.database == nil {
		log.Println("🧹 Database not available, skipping data maintenance scheduler")
		return nil
	}

	// Parse cleanup time (format: HH:MM)
	timeParts := strings.Split(dm.config.CleanupTime, ":")
	if len(timeParts) != 2 {
		return fmt.Errorf("invalid cleanup time format: %s (expected HH:MM)", dm.config.CleanupTime)
	}

	hour, err := strconv.Atoi(timeParts[0])
	if err != nil || hour < 0 || hour > 23 {
		return fmt.Errorf("invalid hour in cleanup time: %s", timeParts[0])
	}

	minute, err := strconv.Atoi(timeParts[1])
	if err != nil || minute < 0 || minute > 59 {
		return fmt.Errorf("invalid minute in cleanup time: %s", timeParts[1])
	}

	// Build cron expression based on workday pattern
	cronExpr := dm.buildCronExpression(hour, minute)

	// Add the cleanup job
	_, err = dm.cron.AddFunc(cronExpr, dm.performCleanup)
	if err != nil {
		return fmt.Errorf("failed to add cleanup job: %w", err)
	}

	// Start the cron scheduler
	dm.cron.Start()

	// Calculate next run time
	entries := dm.cron.Entries()
	if len(entries) > 0 {
		nextRun := entries[0].Next
		log.Printf("🧹 Data maintenance scheduler started - will run at %s", dm.config.CleanupTime)
		log.Printf("🧹 Next cleanup: %s", nextRun.Format("2006-01-02 15:04:05 MST"))
		log.Printf("🧹 Retention period: %d days", dm.config.RetentionDays)
	}

	return nil
}

// Stop stops the data maintenance scheduler
func (dm *DataMaintenanceService) Stop() {
	if dm.cron != nil {
		dm.cron.Stop()
		log.Println("🧹 Data maintenance scheduler stopped")
	}
}

// buildCronExpression builds a cron expression based on workday pattern
func (dm *DataMaintenanceService) buildCronExpression(hour, minute int) string {
	// Base cron expression: minute hour * * day_of_week
	baseExpr := fmt.Sprintf("%d %d * * ", minute, hour)

	if !dm.config.WorkdaysOnly {
		// Run every day
		return baseExpr + "*"
	}

	// Handle workday patterns
	switch strings.ToUpper(dm.config.WorkdayPattern) {
	case "DAILY":
		return baseExpr + "*"
	case "MON-FRI":
		return baseExpr + "1-5" // Monday to Friday
	case "MON-SAT":
		return baseExpr + "1-6" // Monday to Saturday
	case "MON-THU":
		return baseExpr + "1-4" // Monday to Thursday
	case "TUE-FRI":
		return baseExpr + "2-5" // Tuesday to Friday
	default:
		// Handle custom patterns like "MON,WED,FRI"
		if strings.Contains(dm.config.WorkdayPattern, ",") {
			days := strings.Split(dm.config.WorkdayPattern, ",")
			var cronDays []string
			dayMap := map[string]string{
				"MON": "1", "TUE": "2", "WED": "3", "THU": "4",
				"FRI": "5", "SAT": "6", "SUN": "0",
			}
			for _, day := range days {
				day = strings.TrimSpace(strings.ToUpper(day))
				if cronDay, exists := dayMap[day]; exists {
					cronDays = append(cronDays, cronDay)
				}
			}
			if len(cronDays) > 0 {
				return baseExpr + strings.Join(cronDays, ",")
			}
		}
		// Default to Monday-Friday if pattern is invalid
		log.Printf("⚠️  Invalid workday pattern '%s', defaulting to MON-FRI", dm.config.WorkdayPattern)
		return baseExpr + "1-5"
	}
}

// performCleanup performs the actual data cleanup
func (dm *DataMaintenanceService) performCleanup() {
	log.Println("🧹 ========== Starting Data Maintenance ==========")
	startTime := time.Now()

	if dm.database == nil {
		log.Println("⚠️  Database not available, skipping cleanup")
		return
	}

	// Get data stats before cleanup
	statsBefore, err := dm.database.GetDataStats()
	if err != nil {
		log.Printf("⚠️  Failed to get data stats before cleanup: %v", err)
	} else {
		log.Printf("📊 Data stats before cleanup: %d total records", statsBefore["total_records"])
		if oldestRecord, ok := statsBefore["oldest_record"].(string); ok && oldestRecord != "" {
			log.Printf("📊 Oldest record: %s", oldestRecord)
		}
	}

	// Perform cleanup
	deletedCount, err := dm.database.CleanupOldData(dm.config.RetentionDays)
	if err != nil {
		log.Printf("❌ Data cleanup failed: %v", err)
		return
	}

	// Get data stats after cleanup
	statsAfter, err := dm.database.GetDataStats()
	if err != nil {
		log.Printf("⚠️  Failed to get data stats after cleanup: %v", err)
	} else {
		log.Printf("📊 Data stats after cleanup: %d total records", statsAfter["total_records"])
		if dbSizeMB, ok := statsAfter["database_size_mb"].(float64); ok {
			log.Printf("📊 Database size: %.2f MB", dbSizeMB)
		}
	}

	duration := time.Since(startTime)
	log.Printf("✅ Data maintenance completed in %v", duration)
	log.Printf("🧹 Removed %d records older than %d days", deletedCount, dm.config.RetentionDays)
	log.Println("🧹 ========== Data Maintenance Complete ==========")
}

// GetNextCleanupTime returns the next scheduled cleanup time
func (dm *DataMaintenanceService) GetNextCleanupTime() *time.Time {
	if dm.cron == nil {
		return nil
	}

	entries := dm.cron.Entries()
	if len(entries) == 0 {
		return nil
	}

	nextRun := entries[0].Next
	return &nextRun
}

// ManualCleanup performs an immediate cleanup (for testing or manual trigger)
func (dm *DataMaintenanceService) ManualCleanup() (int, error) {
	if dm.database == nil {
		return 0, fmt.Errorf("database not available")
	}

	log.Println("🧹 Performing manual data cleanup...")
	return dm.database.CleanupOldData(dm.config.RetentionDays)
}
