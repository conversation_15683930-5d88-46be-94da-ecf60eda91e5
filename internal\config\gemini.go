package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// GeminiConfig holds Gemini AI configuration
type GeminiConfig struct {
	// API 配置
	APIKey          string `json:"api_key"`
	Model           string `json:"model"`
	Enabled         bool   `json:"enabled"`
	AnalysisEnabled bool   `json:"analysis_enabled"`
	TimeoutSeconds  int    `json:"timeout_seconds"`

	// 代理配置
	ProxyEnabled bool   `json:"proxy_enabled"`
	ProxyURL     string `json:"proxy_url"`

	// 分析配置
	MaxWords       int    `json:"max_words"`
	Language       string `json:"language"`
	AnalysisPoints int    `json:"analysis_points"`

	// 提示词配置
	AnalysisTitle  string `json:"analysis_title"`
	PromptTemplate string `json:"prompt_template"`

	// 错误处理配置
	RetryCount      int    `json:"retry_count"`
	FallbackMessage string `json:"fallback_message"`
}

// LoadGeminiConfig loads Gemini configuration from .env.gemini file
func LoadGeminiConfig() (*GeminiConfig, error) {
	// Load .env.gemini file
	err := godotenv.Load(".env.gemini")
	if err != nil {
		log.Printf("⚠️  Warning: Could not load .env.gemini file: %v", err)
		// Continue with default values if file doesn't exist
	}

	config := &GeminiConfig{
		// API 配置
		APIKey:          getGeminiEnv("GEMINI_API_KEY", ""),
		Model:           getGeminiEnv("GEMINI_MODEL", "gemini-2.0-flash-lite"),
		Enabled:         getGeminiBoolEnv("GEMINI_ENABLED", true),
		AnalysisEnabled: getGeminiBoolEnv("GEMINI_ANALYSIS_ENABLED", true),
		TimeoutSeconds:  getGeminiIntEnv("GEMINI_TIMEOUT_SECONDS", 30),

		// 代理配置
		ProxyEnabled: getGeminiBoolEnv("GEMINI_PROXY_ENABLED", false),
		ProxyURL:     getGeminiEnv("GEMINI_PROXY_URL", ""),

		// 分析配置
		MaxWords:       getGeminiIntEnv("GEMINI_MAX_WORDS", 200),
		Language:       getGeminiEnv("GEMINI_LANGUAGE", "中文"),
		AnalysisPoints: getGeminiIntEnv("GEMINI_ANALYSIS_POINTS", 3),

		// 提示词配置
		AnalysisTitle:  getGeminiEnv("GEMINI_ANALYSIS_TITLE", "🧠 AI 趋势洞察"),
		PromptTemplate: getGeminiEnv("GEMINI_PROMPT_TEMPLATE", getDefaultPromptTemplate()),

		// 错误处理配置
		RetryCount:      getGeminiIntEnv("GEMINI_RETRY_COUNT", 2),
		FallbackMessage: getGeminiEnv("GEMINI_FALLBACK_MESSAGE", "AI分析暂时不可用"),
	}

	// Validate required fields
	if config.Enabled && config.APIKey == "" {
		log.Println("⚠️  Warning: GEMINI_API_KEY is not set, Gemini features will be disabled")
		config.Enabled = false
		config.AnalysisEnabled = false
	}

	log.Printf("🤖 Gemini config loaded - Enabled: %v, Analysis: %v, Model: %s, Proxy: %v",
		config.Enabled, config.AnalysisEnabled, config.Model, config.ProxyEnabled)

	if config.ProxyEnabled && config.ProxyURL != "" {
		log.Printf("🌐 Gemini proxy configured: %s", config.ProxyURL)
	}

	return config, nil
}

// getGeminiEnv gets environment variable with default value
func getGeminiEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getGeminiBoolEnv gets boolean environment variable with default value
func getGeminiBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getGeminiIntEnv gets integer environment variable with default value
func getGeminiIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getDefaultPromptTemplate returns the default prompt template
func getDefaultPromptTemplate() string {
	return `请分析以下Google Trends数据，提供简洁的洞察和趋势分析：

{TRENDS_DATA}

请提供：
1. 主要趋势洞察（{ANALYSIS_POINTS}个要点）
2. 跨地区对比分析
3. 值得关注的热门话题

要求：
- 回复简洁明了，控制在{MAX_WORDS}字以内
- 使用{LANGUAGE}回复
- 重点关注数据背后的含义和趋势
- 避免重复数据本身的内容`
}
