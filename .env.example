# SerpApi Configuration
# SERPAPI_KEY removed - use API keys directly in task URLs

# Server Configuration
PORT=9099
GIN_MODE=debug

# API Configuration (minimal - most params now in task URLs)
MAX_QUERIES_PER_REQUEST=5

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIME=09:01
SCHEDULER_TIMEZONE=Asia/Shanghai
SCHEDULER_WORKDAYS_ONLY=true
SCHEDULER_WORKDAY_PATTERN=MON-FRI

# Task Groups Configuration
TASK_GROUPS_ENABLED=true

# 任务配置格式 - 直接使用完整SerpAPI URL
# 格式: 任务名称=完整的SerpAPI请求URL(包含api_key)
# 注意：变量名必须使用英文，不能使用中文字符
# 示例:
# CHINA_EXPLORE_UNDERWEAR=https://serpapi.com/search.json?engine=google_trends&q=内衣&hl=zh-cn&geo=CN&data_type=RELATED_TOPICS&date=now+7-d&api_key=your_api_key_here
# US_TRENDING_NOW=https://serpapi.com/search.json?engine=google_trends_trending_now&geo=US&hours=24&hl=zh-cn&api_key=your_api_key_here

# Web Interface Configuration
WEB_INTERFACE_ENABLED=true
WEB_INTERFACE_PATH=/admin

# Database Configuration
DATABASE_ENABLED=true
DATABASE_PATH=./data/trends.db

# Data Maintenance Configuration
DATA_MAINTENANCE_ENABLED=true
DATA_RETENTION_DAYS=5
DATA_CLEANUP_TIME=02:00
DATA_CLEANUP_WORKDAYS_ONLY=false
DATA_CLEANUP_WORKDAY_PATTERN=DAILY

# Windows Keep-Alive Configuration (防止Windows效能模式影响)
KEEPALIVE_ENABLED=true
KEEPALIVE_HEARTBEAT_INTERVAL=5m
KEEPALIVE_NETWORK_HEARTBEAT=true
KEEPALIVE_HEARTBEAT_URL=https://www.google.com/generate_204
KEEPALIVE_PROCESS_PRIORITY=high
KEEPALIVE_PREVENT_SLEEP=true
KEEPALIVE_PREVENT_HIBERNATE=true
KEEPALIVE_LOG_INTERVAL=10m
KEEPALIVE_MONITOR_RESOURCES=true
