package services

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/models"
)

// TrendsAnalyzer provides analysis capabilities for trends data
type TrendsAnalyzer struct{}

// NewTrendsAnalyzer creates a new trends analyzer
func NewTrendsAnalyzer() *TrendsAnalyzer {
	return &TrendsAnalyzer{}
}

// AnalyzeTrends performs comprehensive analysis on trends data
func (a *TrendsAnalyzer) AnalyzeTrends(data *models.TrendsResponse, query string) *models.TrendsAnalysis {
	analysis := &models.TrendsAnalysis{
		Query:       query,
		DataType:    data.SearchParameters.DataType,
		GeneratedAt: time.Now(),
		RawData:     data,
	}

	// Analyze based on data type
	switch data.SearchParameters.DataType {
	case "TIMESERIES":
		a.analyzeTimeSeries(analysis, data)
	case "GEO_MAP_0":
		a.analyzeRegionInterest(analysis, data)
	case "RELATED_TOPICS":
		a.analyzeRelatedTopics(analysis, data)
	case "RELATED_QUERIES":
		a.analyzeRelatedQueries(analysis, data)
	case "GEO_MAP":
		a.analyzeComparedRegions(analysis, data)
	}

	// Generate summary
	analysis.Summary = a.generateSummary(analysis)

	return analysis
}

// analyzeTimeSeries analyzes time series data
func (a *TrendsAnalyzer) analyzeTimeSeries(analysis *models.TrendsAnalysis, data *models.TrendsResponse) {
	if data.InterestOverTime == nil || len(data.InterestOverTime.TimelineData) == 0 {
		return
	}

	timeline := data.InterestOverTime.TimelineData
	
	// Determine trend direction
	analysis.TrendDirection = a.calculateTrendDirection(timeline)
	
	// Find peak periods
	analysis.PeakPeriods = a.findPeakPeriods(timeline)
	
	// Generate insights
	insights := []string{}
	
	// Average analysis
	if len(data.InterestOverTime.Averages) > 0 {
		for _, avg := range data.InterestOverTime.Averages {
			insights = append(insights, fmt.Sprintf("Average interest for '%s': %d", avg.Query, avg.Value))
		}
	}
	
	// Volatility analysis
	volatility := a.calculateVolatility(timeline)
	if volatility > 20 {
		insights = append(insights, "High volatility detected - search interest fluctuates significantly")
	} else if volatility < 5 {
		insights = append(insights, "Low volatility - search interest is relatively stable")
	}
	
	analysis.KeyInsights = insights
}

// analyzeRegionInterest analyzes regional interest data
func (a *TrendsAnalyzer) analyzeRegionInterest(analysis *models.TrendsAnalysis, data *models.TrendsResponse) {
	if len(data.InterestByRegion) == 0 {
		return
	}
	
	// Sort regions by interest level
	regions := make([]models.RegionInterest, len(data.InterestByRegion))
	copy(regions, data.InterestByRegion)
	sort.Slice(regions, func(i, j int) bool {
		return regions[i].ExtractedValue > regions[j].ExtractedValue
	})
	
	// Get top regions
	topCount := 5
	if len(regions) < topCount {
		topCount = len(regions)
	}
	
	for i := 0; i < topCount; i++ {
		analysis.TopRegions = append(analysis.TopRegions, 
			fmt.Sprintf("%s (%d)", regions[i].Location, regions[i].ExtractedValue))
	}
	
	// Generate insights
	insights := []string{}
	if len(regions) > 0 {
		insights = append(insights, fmt.Sprintf("Highest interest region: %s with %d%% relative interest", 
			regions[0].Location, regions[0].ExtractedValue))
		
		if len(regions) > 1 {
			insights = append(insights, fmt.Sprintf("Second highest: %s with %d%% relative interest", 
				regions[1].Location, regions[1].ExtractedValue))
		}
	}
	
	analysis.KeyInsights = insights
}

// analyzeRelatedTopics analyzes related topics data
func (a *TrendsAnalyzer) analyzeRelatedTopics(analysis *models.TrendsAnalysis, data *models.TrendsResponse) {
	if data.RelatedTopics == nil {
		return
	}
	
	insights := []string{}
	
	// Analyze rising topics
	if len(data.RelatedTopics.Rising) > 0 {
		insights = append(insights, fmt.Sprintf("Found %d rising related topics", len(data.RelatedTopics.Rising)))
		
		// Get top rising topic
		topRising := data.RelatedTopics.Rising[0]
		insights = append(insights, fmt.Sprintf("Top rising topic: '%s' (+%d%%)", 
			topRising.Topic.Title, topRising.ExtractedValue))
	}
	
	// Analyze top topics
	if len(data.RelatedTopics.Top) > 0 {
		insights = append(insights, fmt.Sprintf("Found %d top related topics", len(data.RelatedTopics.Top)))
		
		// Extract topic titles for keywords
		for i, topic := range data.RelatedTopics.Top {
			if i < 5 { // Top 5 topics
				analysis.RelatedKeywords = append(analysis.RelatedKeywords, topic.Topic.Title)
			}
		}
	}
	
	analysis.KeyInsights = insights
}

// analyzeRelatedQueries analyzes related queries data
func (a *TrendsAnalyzer) analyzeRelatedQueries(analysis *models.TrendsAnalysis, data *models.TrendsResponse) {
	if data.RelatedQueries == nil {
		return
	}
	
	insights := []string{}
	
	// Analyze rising queries
	if len(data.RelatedQueries.Rising) > 0 {
		insights = append(insights, fmt.Sprintf("Found %d rising related queries", len(data.RelatedQueries.Rising)))
		
		// Get top rising query
		topRising := data.RelatedQueries.Rising[0]
		insights = append(insights, fmt.Sprintf("Top rising query: '%s' (+%d%%)", 
			topRising.Query, topRising.ExtractedValue))
	}
	
	// Analyze top queries
	if len(data.RelatedQueries.Top) > 0 {
		insights = append(insights, fmt.Sprintf("Found %d top related queries", len(data.RelatedQueries.Top)))
		
		// Extract queries for keywords
		for i, query := range data.RelatedQueries.Top {
			if i < 5 { // Top 5 queries
				analysis.RelatedKeywords = append(analysis.RelatedKeywords, query.Query)
			}
		}
	}
	
	analysis.KeyInsights = insights
}

// analyzeComparedRegions analyzes compared regional data
func (a *TrendsAnalyzer) analyzeComparedRegions(analysis *models.TrendsAnalysis, data *models.TrendsResponse) {
	if len(data.ComparedBreakdown) == 0 {
		return
	}
	
	insights := []string{}
	insights = append(insights, fmt.Sprintf("Analyzed %d regions for query comparison", len(data.ComparedBreakdown)))
	
	// Find regions where each query dominates
	queryDominance := make(map[string][]string)
	
	for _, region := range data.ComparedBreakdown {
		if len(region.Values) > 0 {
			maxValue := 0
			dominantQuery := ""
			
			for _, value := range region.Values {
				if value.ExtractedValue > maxValue {
					maxValue = value.ExtractedValue
					dominantQuery = value.Query
				}
			}
			
			if dominantQuery != "" {
				queryDominance[dominantQuery] = append(queryDominance[dominantQuery], region.Location)
			}
		}
	}
	
	// Generate insights about dominance
	for query, regions := range queryDominance {
		if len(regions) > 0 {
			insights = append(insights, fmt.Sprintf("'%s' dominates in %d regions", query, len(regions)))
		}
	}
	
	analysis.KeyInsights = insights
}

// calculateTrendDirection determines if trend is rising, falling, or stable
func (a *TrendsAnalyzer) calculateTrendDirection(timeline []models.TimelineData) string {
	if len(timeline) < 2 {
		return "insufficient_data"
	}
	
	// Compare first quarter with last quarter
	quarterSize := len(timeline) / 4
	if quarterSize < 1 {
		quarterSize = 1
	}
	
	firstQuarterAvg := a.calculateAverageValue(timeline[:quarterSize])
	lastQuarterAvg := a.calculateAverageValue(timeline[len(timeline)-quarterSize:])
	
	diff := lastQuarterAvg - firstQuarterAvg
	
	if diff > 10 {
		return "rising"
	} else if diff < -10 {
		return "falling"
	} else {
		return "stable"
	}
}

// findPeakPeriods identifies periods with highest search interest
func (a *TrendsAnalyzer) findPeakPeriods(timeline []models.TimelineData) []string {
	if len(timeline) == 0 {
		return nil
	}
	
	var peaks []string
	maxValue := 0
	
	// Find maximum value
	for _, point := range timeline {
		if len(point.Values) > 0 {
			if point.Values[0].ExtractedValue > maxValue {
				maxValue = point.Values[0].ExtractedValue
			}
		}
	}
	
	// Find periods within 90% of max value
	threshold := int(float64(maxValue) * 0.9)
	
	for _, point := range timeline {
		if len(point.Values) > 0 {
			if point.Values[0].ExtractedValue >= threshold {
				peaks = append(peaks, point.Date)
			}
		}
	}
	
	return peaks
}

// calculateVolatility measures how much the values fluctuate
func (a *TrendsAnalyzer) calculateVolatility(timeline []models.TimelineData) float64 {
	if len(timeline) < 2 {
		return 0
	}
	
	values := []float64{}
	for _, point := range timeline {
		if len(point.Values) > 0 {
			values = append(values, float64(point.Values[0].ExtractedValue))
		}
	}
	
	if len(values) < 2 {
		return 0
	}
	
	// Calculate standard deviation
	mean := 0.0
	for _, v := range values {
		mean += v
	}
	mean /= float64(len(values))
	
	variance := 0.0
	for _, v := range values {
		variance += (v - mean) * (v - mean)
	}
	variance /= float64(len(values))
	
	return variance / mean * 100 // Return as percentage
}

// calculateAverageValue calculates average value for a slice of timeline data
func (a *TrendsAnalyzer) calculateAverageValue(timeline []models.TimelineData) float64 {
	if len(timeline) == 0 {
		return 0
	}

	total := 0.0
	count := 0

	for _, point := range timeline {
		if len(point.Values) > 0 {
			total += float64(point.Values[0].ExtractedValue)
			count++
		}
	}

	if count == 0 {
		return 0
	}

	return total / float64(count)
}

// generateSummary creates a human-readable summary of the analysis
func (a *TrendsAnalyzer) generateSummary(analysis *models.TrendsAnalysis) string {
	var summary strings.Builder

	summary.WriteString(fmt.Sprintf("Analysis for '%s' (%s): ", analysis.Query, analysis.DataType))

	if analysis.TrendDirection != "" {
		summary.WriteString(fmt.Sprintf("Trend: %s. ", analysis.TrendDirection))
	}

	if len(analysis.TopRegions) > 0 {
		summary.WriteString(fmt.Sprintf("Top regions: %s. ", strings.Join(analysis.TopRegions[:min(3, len(analysis.TopRegions))], ", ")))
	}

	if len(analysis.PeakPeriods) > 0 {
		summary.WriteString(fmt.Sprintf("%d peak periods identified. ", len(analysis.PeakPeriods)))
	}

	if len(analysis.RelatedKeywords) > 0 {
		summary.WriteString(fmt.Sprintf("Related: %s. ", strings.Join(analysis.RelatedKeywords[:min(3, len(analysis.RelatedKeywords))], ", ")))
	}

	return summary.String()
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
