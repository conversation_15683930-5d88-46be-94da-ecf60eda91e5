package handlers

import (
	"encoding/json"
	"io"
	"log"
	"net/http"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/services"

	"github.com/gin-gonic/gin"
)

// DingTalkWebhookHandler handles DingTalk robot webhook messages
type DingTalkWebhookHandler struct {
	config          *config.Config
	dingTalkService *services.DingTalkService
}

// NewDingTalkWebhookHandler creates a new DingTalk webhook handler
func NewDingTalkWebhookHandler(config *config.Config, dingTalkService *services.DingTalkService) *DingTalkWebhookHandler {
	return &DingTalkWebhookHandler{
		config:          config,
		dingTalkService: dingTalkService,
	}
}

// DingTalkWebhookMessage represents the structure of incoming DingTalk messages
type DingTalkWebhookMessage struct {
	MsgType string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
	SenderID                  string `json:"senderId"`
	SenderNick                string `json:"senderNick"`
	IsAdmin                   bool   `json:"isAdmin"`
	SenderStaffId             string `json:"senderStaffId"`
	ChatbotCorpId             string `json:"chatbotCorpId"`
	ChatbotUserId             string `json:"chatbotUserId"`
	MsgId                     string `json:"msgId"`
	CreateAt                  int64  `json:"createAt"`
	ConversationType          string `json:"conversationType"`
	ConversationId            string `json:"conversationId"`
	ConversationTitle         string `json:"conversationTitle"`
	IsInAtList                bool   `json:"isInAtList"`
	SenderCorpId              string `json:"senderCorpId"`
	SessionWebhook            string `json:"sessionWebhook"`
	SessionWebhookExpiredTime int64  `json:"sessionWebhookExpiredTime"`
}

// HandleWebhook handles incoming DingTalk webhook messages
func (h *DingTalkWebhookHandler) HandleWebhook(c *gin.Context) {
	// 验证请求签名
	if !h.verifySignature(c) {
		log.Println("❌ DingTalk webhook signature verification failed")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.Printf("❌ Failed to read request body: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	log.Printf("📱 Received DingTalk webhook: %s", string(body))

	// 解析消息
	var message DingTalkWebhookMessage
	if err := json.Unmarshal(body, &message); err != nil {
		log.Printf("❌ Failed to parse message: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse message"})
		return
	}

	log.Printf("📱 Parsed message successfully: sender=%s, type=%s, content=%s",
		message.SenderNick, message.ConversationType, message.Text.Content)

	// 处理消息
	h.processMessage(&message)

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{"success": true})
}

// verifySignature verifies the DingTalk webhook signature
func (h *DingTalkWebhookHandler) verifySignature(c *gin.Context) bool {
	// 获取签名相关头部
	timestamp := c.GetHeader("timestamp")
	sign := c.GetHeader("sign")

	// 暂时跳过签名验证，用于测试
	// TODO: 实现正确的签名验证
	log.Printf("📱 签名验证: timestamp=%s, sign=%s", timestamp, sign)
	log.Println("⚠️  Skipping signature verification for testing")
	return true
}

// processMessage processes the incoming message and sends auto-reply
func (h *DingTalkWebhookHandler) processMessage(message *DingTalkWebhookMessage) {
	log.Printf("📱 Processing message from %s (%s): %s",
		message.SenderNick, message.SenderID, message.Text.Content)

	// 自动收集用户ID和发送自动回复
	h.handleIncomingMessage(message)
}

// handleIncomingMessage handles the incoming message, collects IDs and sends auto-reply
func (h *DingTalkWebhookHandler) handleIncomingMessage(message *DingTalkWebhookMessage) {
	log.Printf("📱 Handling incoming message from %s (%s) in conversation %s (type: %s)",
		message.SenderNick, message.SenderID, message.ConversationId, message.ConversationType)

	// 使用现有的HandleIncomingMessage方法来处理消息和收集ID
	err := h.dingTalkService.HandleIncomingMessage(
		message.SenderID,
		message.SenderNick,
		message.ConversationId,
		message.ConversationType,
		message.Text.Content,
	)

	if err != nil {
		log.Printf("❌ Failed to handle incoming message: %v", err)
	} else {
		log.Printf("✅ Successfully handled incoming message from %s", message.SenderNick)
	}
}
