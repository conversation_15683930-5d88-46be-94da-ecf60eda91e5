package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/models"
)

// SerpApiService handles interactions with SerpApi Google Trends API
type SerpApiService struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	config     *config.Config
}

// NewSerpApiService creates a new SerpApi service instance
func NewSerpApiService(apiKey string, cfg *config.Config) *SerpApiService {
	return &SerpApiService{
		apiKey:  apiKey,
		baseURL: "https://serpapi.com/search",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		config: cfg,
	}
}

// GetTrends fetches trends data from SerpApi
func (s *SerpApiService) GetTrends(req *models.TrendsRequest) (*models.TrendsResponse, error) {
	// Build query parameters
	params := url.Values{}
	params.Set("engine", "google_trends")
	params.Set("api_key", s.apiKey)
	params.Set("q", req.Query)

	// Set optional parameters (no config defaults - use task URL params directly)
	if req.DataType != "" {
		params.Set("data_type", req.DataType)
	}

	if req.Geo != "" {
		params.Set("geo", req.Geo)
	}

	if req.Date != "" {
		params.Set("date", req.Date)
	}

	if req.Language != "" {
		params.Set("hl", req.Language)
	}

	if req.Category != "" {
		params.Set("cat", req.Category)
	}

	if req.Property != "" {
		params.Set("gprop", req.Property)
	}

	if req.Timezone != "" {
		params.Set("tz", req.Timezone)
	}

	if req.IncludeLowSearchVolume {
		params.Set("include_low_search_volume", "true")
	}

	// Build full URL
	fullURL := fmt.Sprintf("%s?%s", s.baseURL, params.Encode())

	// Make HTTP request
	resp, err := s.httpClient.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse JSON response
	var trendsResp models.TrendsResponse
	if err := json.Unmarshal(body, &trendsResp); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	return &trendsResp, nil
}

// GetInterestOverTime fetches interest over time data
func (s *SerpApiService) GetInterestOverTime(query, geo, date string) (*models.TrendsResponse, error) {
	req := &models.TrendsRequest{
		Query:    query,
		DataType: "TIMESERIES",
		Geo:      geo,
		Date:     date,
	}
	return s.GetTrends(req)
}

// GetInterestByRegion fetches interest by region data
func (s *SerpApiService) GetInterestByRegion(query, geo, date string) (*models.TrendsResponse, error) {
	req := &models.TrendsRequest{
		Query:    query,
		DataType: "GEO_MAP_0",
		Geo:      geo,
		Date:     date,
	}
	return s.GetTrends(req)
}

// GetRelatedTopics fetches related topics data
func (s *SerpApiService) GetRelatedTopics(query, geo, date string) (*models.TrendsResponse, error) {
	req := &models.TrendsRequest{
		Query:    query,
		DataType: "RELATED_TOPICS",
		Geo:      geo,
		Date:     date,
	}
	return s.GetTrends(req)
}

// GetRelatedQueries fetches related queries data
func (s *SerpApiService) GetRelatedQueries(query, geo, date string) (*models.TrendsResponse, error) {
	req := &models.TrendsRequest{
		Query:    query,
		DataType: "RELATED_QUERIES",
		Geo:      geo,
		Date:     date,
	}
	return s.GetTrends(req)
}

// CompareQueries compares multiple queries over time
func (s *SerpApiService) CompareQueries(queries []string, geo, date string) (*models.TrendsResponse, error) {
	if len(queries) == 0 || len(queries) > 5 {
		return nil, fmt.Errorf("queries must contain 1-5 items, got %d", len(queries))
	}

	// Join queries with comma
	queryStr := ""
	for i, q := range queries {
		if i > 0 {
			queryStr += ","
		}
		queryStr += q
	}

	req := &models.TrendsRequest{
		Query:    queryStr,
		DataType: "TIMESERIES",
		Geo:      geo,
		Date:     date,
	}
	return s.GetTrends(req)
}

// CompareByRegion compares multiple queries by region
func (s *SerpApiService) CompareByRegion(queries []string, geo, date string, includeLowVolume bool) (*models.TrendsResponse, error) {
	if len(queries) < 2 || len(queries) > 5 {
		return nil, fmt.Errorf("region comparison requires 2-5 queries, got %d", len(queries))
	}

	// Join queries with comma
	queryStr := ""
	for i, q := range queries {
		if i > 0 {
			queryStr += ","
		}
		queryStr += q
	}

	req := &models.TrendsRequest{
		Query:                  queryStr,
		DataType:               "GEO_MAP",
		Geo:                    geo,
		Date:                   date,
		IncludeLowSearchVolume: includeLowVolume,
	}
	return s.GetTrends(req)
}

// GetTrendingNow fetches trending searches from Google Trends Trending Now
func (s *SerpApiService) GetTrendingNow(geo string, hours int, categoryID string, onlyActive bool) (*models.TrendingNowResponse, error) {
	// Build query parameters
	params := url.Values{}
	params.Set("engine", "google_trends_trending_now")
	params.Set("api_key", s.apiKey)

	// Set geo parameter (required)
	if geo != "" {
		params.Set("geo", geo)
	} else {
		params.Set("geo", "US") // Default fallback
	}

	// Set hours parameter (optional)
	if hours > 0 {
		params.Set("hours", fmt.Sprintf("%d", hours))
	}

	// Set category filter (optional)
	if categoryID != "" {
		params.Set("category_id", categoryID)
	}

	// Set only active filter (optional)
	if onlyActive {
		params.Set("only_active", "true")
	}

	// Build full URL
	fullURL := fmt.Sprintf("%s?%s", s.baseURL, params.Encode())

	// Make HTTP request
	resp, err := s.httpClient.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse JSON response
	var trendingResp models.TrendingNowResponse
	if err := json.Unmarshal(body, &trendingResp); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	return &trendingResp, nil
}

// GetTrendsExplore fetches Google Trends explore data without specific query (like the explore page)
func (s *SerpApiService) GetTrendsExplore(geo, language, timezone, region, category, dataType, date string) (*models.TrendsResponse, error) {
	// Build query parameters for explore mode (no specific query, but with category)
	params := url.Values{}
	params.Set("engine", "google_trends")
	params.Set("api_key", s.apiKey)

	// Set geo parameter (required for explore)
	if geo != "" {
		params.Set("geo", geo)
	}

	// Set language parameter
	if language != "" {
		params.Set("hl", language)
	}

	// Set timezone parameter
	if timezone != "" {
		params.Set("tz", timezone)
	}

	// Set category parameter (optional)
	if category != "" {
		params.Set("cat", category)
	}

	// Set data_type parameter
	if dataType != "" {
		params.Set("data_type", dataType)
	}

	// Set date parameter (time range for explore)
	if date != "" {
		params.Set("date", date)
	} else {
		params.Set("date", "now 7-d") // Default fallback
	}

	// Build full URL
	fullURL := fmt.Sprintf("%s?%s", s.baseURL, params.Encode())

	// Make HTTP request
	resp, err := s.httpClient.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse JSON response
	var trendsResp models.TrendsResponse
	if err := json.Unmarshal(body, &trendsResp); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	return &trendsResp, nil
}

// ExecuteDirectURL executes a SerpAPI request using a direct URL configuration
// This method supports the new URL-style task configuration
func (s *SerpApiService) ExecuteDirectURL(originalURL string) (interface{}, error) {
	// Parse the URL to extract parameters
	parsedURL, err := url.Parse(originalURL)
	if err != nil {
		return nil, fmt.Errorf("invalid URL format: %w", err)
	}

	// Check if api_key is already in the URL, if not add it
	query := parsedURL.Query()
	if query.Get("api_key") == "" {
		query.Set("api_key", s.apiKey)
		parsedURL.RawQuery = query.Encode()
	}

	// Build the final URL
	finalURL := parsedURL.String()

	// Make HTTP request
	resp, err := s.httpClient.Get(finalURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Determine response type based on engine parameter
	engine := query.Get("engine")
	switch engine {
	case "google_trends_trending_now":
		// Parse as TrendingNowResponse
		var trendingResp models.TrendingNowResponse
		if err := json.Unmarshal(body, &trendingResp); err != nil {
			return nil, fmt.Errorf("failed to parse trending now JSON response: %w", err)
		}
		return &trendingResp, nil
	case "google_trends":
		// Parse as TrendsResponse
		var trendsResp models.TrendsResponse
		if err := json.Unmarshal(body, &trendsResp); err != nil {
			return nil, fmt.Errorf("failed to parse trends JSON response: %w", err)
		}
		return &trendsResp, nil
	default:
		// Default to TrendsResponse
		var trendsResp models.TrendsResponse
		if err := json.Unmarshal(body, &trendsResp); err != nil {
			return nil, fmt.Errorf("failed to parse JSON response: %w", err)
		}
		return &trendsResp, nil
	}
}

// ValidateDataType checks if the data type is valid
func (s *SerpApiService) ValidateDataType(dataType string) bool {
	validTypes := map[string]bool{
		"TIMESERIES":      true,
		"GEO_MAP":         true,
		"GEO_MAP_0":       true,
		"RELATED_TOPICS":  true,
		"RELATED_QUERIES": true,
	}
	return validTypes[dataType]
}

// ValidateTimezone validates timezone parameter
func (s *SerpApiService) ValidateTimezone(tz string) bool {
	if tz == "" {
		return true // Empty is valid (uses default)
	}

	tzInt, err := strconv.Atoi(tz)
	if err != nil {
		return false
	}

	return tzInt >= -1439 && tzInt <= 1439
}
