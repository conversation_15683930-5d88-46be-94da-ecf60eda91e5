package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "modernc.org/sqlite"
)

func main() {
	// 检查数据库文件是否存在
	dbPath := "./data/trends.db"
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		fmt.Printf("数据库文件不存在: %s\n", dbPath)
		return
	}

	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		log.Fatal("打开数据库失败:", err)
	}
	defer db.Close()

	// 首先检查当前数据
	fmt.Println("=== 清空前的数据统计 ===")
	var totalCount int
	err = db.QueryRow("SELECT COUNT(*) FROM trends_data").Scan(&totalCount)
	if err != nil {
		log.Fatal("查询记录数失败:", err)
	}
	fmt.Printf("总记录数: %d\n", totalCount)

	// 检查有batch_id的记录数
	var batchCount int
	err = db.QueryRow("SELECT COUNT(*) FROM trends_data WHERE batch_id IS NOT NULL").Scan(&batchCount)
	if err != nil {
		log.Fatal("查询batch记录数失败:", err)
	}
	fmt.Printf("有batch_id的记录数: %d\n", batchCount)

	if totalCount == 0 {
		fmt.Println("数据库已经是空的，无需清空。")
		return
	}

	// 清空数据表
	fmt.Println("\n=== 开始清空数据 ===")
	result, err := db.Exec("DELETE FROM trends_data")
	if err != nil {
		log.Fatal("清空数据失败:", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Printf("获取影响行数失败: %v", err)
	} else {
		fmt.Printf("删除了 %d 条记录\n", rowsAffected)
	}

	// 重置自增ID
	_, err = db.Exec("DELETE FROM sqlite_sequence WHERE name='trends_data'")
	if err != nil {
		log.Printf("重置自增ID失败（可能表不存在自增字段）: %v", err)
	} else {
		fmt.Println("重置自增ID成功")
	}

	// 验证清空结果
	fmt.Println("\n=== 清空后的数据统计 ===")
	err = db.QueryRow("SELECT COUNT(*) FROM trends_data").Scan(&totalCount)
	if err != nil {
		log.Fatal("验证清空结果失败:", err)
	}
	fmt.Printf("总记录数: %d\n", totalCount)

	if totalCount == 0 {
		fmt.Println("✅ 数据库清空成功！")
	} else {
		fmt.Printf("❌ 数据库清空失败，还有 %d 条记录\n", totalCount)
		return
	}

	fmt.Println("\n=== 数据库表结构验证 ===")
	rows, err := db.Query("PRAGMA table_info(trends_data)")
	if err != nil {
		log.Fatal("查询表结构失败:", err)
	}
	defer rows.Close()

	fmt.Println("字段列表:")
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue sql.NullString

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			log.Fatal("扫描表结构失败:", err)
		}

		fmt.Printf("  %d. %s (%s)", cid, name, dataType)
		if notNull == 1 {
			fmt.Printf(" NOT NULL")
		}
		if pk == 1 {
			fmt.Printf(" PRIMARY KEY")
		}
		fmt.Println()
	}

	fmt.Println("\n🎉 数据库已清空，可以重新测试批次化存储功能！")
	fmt.Println("\n📝 接下来你可以：")
	fmt.Println("1. 重新启动服务器")
	fmt.Println("2. 执行香港实时热门任务")
	fmt.Println("3. 验证层次化数据存储是否正常工作")
}
