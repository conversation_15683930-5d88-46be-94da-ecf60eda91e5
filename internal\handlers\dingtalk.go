package handlers

import (
	"bufio"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/services"

	"github.com/gin-gonic/gin"
)

// DingTalkHandler handles DingTalk related requests
type DingTalkHandler struct {
	dingTalkService *services.DingTalkService
	database        *services.DatabaseService
}

// NewDingTalkHandler creates a new DingTalk handler
func NewDingTalkHandler(dingTalkService *services.DingTalkService, database *services.DatabaseService) *DingTalkHandler {
	return &DingTalkHandler{
		dingTalkService: dingTalkService,
		database:        database,
	}
}

// GetRecipients returns all DingTalk recipients from configuration file
func (h *DingTalkHandler) GetRecipients(c *gin.Context) {
	recipients, err := h.dingTalkService.GetRecipientsFromConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, recipients)
}

// AddRecipient is deprecated - recipients are now managed through configuration file
func (h *DingTalkHandler) AddRecipient(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{
		"error":   "Adding recipients through API is no longer supported. Please edit the .env.dingtalk configuration file instead.",
		"message": "Recipients are now managed through DINGTALK_USERS and DINGTALK_GROUPS in .env.dingtalk file",
	})
}

// SendManualPush sends a manual push notification
func (h *DingTalkHandler) SendManualPush(c *gin.Context) {
	var request struct {
		RecordIDs    []int    `json:"record_ids" binding:"required"`
		RecipientIDs []string `json:"recipient_ids"`
		TaskGroup    string   `json:"task_group"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get records by IDs
	records, err := h.getRecordsByIDs(request.RecordIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if len(records) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No records found"})
		return
	}

	// Determine task group name
	taskGroupName := request.TaskGroup
	if taskGroupName == "" && len(records) > 0 {
		taskGroupName = records[0].TaskGroup
	}

	// Format message
	message := h.dingTalkService.FormatTrendsMessage(taskGroupName, records)

	// Send message
	var sendErr error
	if len(request.RecipientIDs) > 0 {
		sendErr = h.dingTalkService.SendMessage(message, request.RecipientIDs)
	} else {
		sendErr = h.dingTalkService.SendToAllRecipients(message)
	}

	if sendErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": sendErr.Error()})
		return
	}

	// Mark records as pushed
	err = h.dingTalkService.MarkRecordsAsPushed(request.RecordIDs)
	if err != nil {
		// Log error but don't fail the request
		// The message was sent successfully
		c.JSON(http.StatusOK, gin.H{
			"message": "Message sent successfully, but failed to mark records as pushed",
			"warning": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Message sent successfully",
		"records":    len(records),
		"recipients": len(request.RecipientIDs),
		"task_group": taskGroupName,
	})
}

// GetUnpushedRecords returns records that haven't been pushed yet
func (h *DingTalkHandler) GetUnpushedRecords(c *gin.Context) {
	hoursStr := c.DefaultQuery("hours", "24")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid hours parameter"})
		return
	}

	records, err := h.dingTalkService.GetUnpushedRecords(hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, records)
}

// PreviewMessage generates a preview of the push message
func (h *DingTalkHandler) PreviewMessage(c *gin.Context) {
	var request struct {
		RecordIDs []int  `json:"record_ids" binding:"required"`
		TaskGroup string `json:"task_group"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get records by IDs
	records, err := h.getRecordsByIDs(request.RecordIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Determine task group name
	taskGroupName := request.TaskGroup
	if taskGroupName == "" && len(records) > 0 {
		taskGroupName = records[0].TaskGroup
	}

	// Format message
	message := h.dingTalkService.FormatTrendsMessage(taskGroupName, records)

	c.JSON(http.StatusOK, gin.H{
		"message":    message,
		"records":    len(records),
		"task_group": taskGroupName,
	})
}

// SendTestMessage sends a test message
func (h *DingTalkHandler) SendTestMessage(c *gin.Context) {
	var request struct {
		Message      string   `json:"message" binding:"required"`
		RecipientIDs []string `json:"recipient_ids"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create a simple test message
	testMessage := &services.DingTalkMessage{
		Title:       "📱 钉钉推送测试",
		Content:     fmt.Sprintf("# 📱 钉钉推送测试\n\n%s\n\n---\n🤖 测试时间: %s", request.Message, time.Now().Format("2006-01-02 15:04:05")),
		MessageType: "markdown",
	}

	// Send message
	var sendErr error
	if len(request.RecipientIDs) > 0 {
		sendErr = h.dingTalkService.SendMessage(testMessage, request.RecipientIDs)
	} else {
		sendErr = h.dingTalkService.SendToAllRecipients(testMessage)
	}

	if sendErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": sendErr.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "测试消息发送成功",
		"recipients": len(request.RecipientIDs),
		"content":    request.Message,
	})
}

// GetConfig returns DingTalk configuration for frontend
func (h *DingTalkHandler) GetConfig(c *gin.Context) {
	config := gin.H{
		"default_test_user":       h.dingTalkService.GetTestUserID(),
		"default_test_user_name":  "测试用户",
		"default_test_group":      h.dingTalkService.GetTestGroupID(),
		"default_test_group_name": "测试群组",
		"bot_enabled":             true, // You can get this from service config if needed
	}

	c.JSON(http.StatusOK, config)
}

// DetectUserID detects current user ID (placeholder for now)
func (h *DingTalkHandler) DetectUserID(c *gin.Context) {
	// For now, this is a placeholder since detecting user ID requires
	// the user to interact with the bot first or use DingTalk's JS SDK
	c.JSON(http.StatusOK, gin.H{
		"message": "用户ID检测功能需要用户先与机器人交互",
		"note":    "请在钉钉中给机器人发送消息，然后查看后端日志获取用户ID",
		"user_id": nil,
	})
}

// TestConnection tests DingTalk API connection
func (h *DingTalkHandler) TestConnection(c *gin.Context) {
	result := gin.H{
		"config_status": "✅ 配置已加载",
		"token_valid":   false,
		"api_status":    "未测试",
		"api_info": gin.H{
			"user_api":  "https://api.dingtalk.com/v1.0/robot/oToMessages/batchSend",
			"group_api": "https://api.dingtalk.com/v1.0/robot/groupMessages/send",
			"auth_api":  "https://oapi.dingtalk.com/gettoken",
			"version":   "v2.0 (1v1聊天) + v1.0 (认证)",
		},
	}

	// Test getting access token
	token, err := h.dingTalkService.GetAccessToken()
	if err != nil {
		result["error"] = err.Error()
		result["token_valid"] = false
		result["api_status"] = "❌ Token获取失败"
	} else {
		result["token_valid"] = true
		result["api_status"] = "✅ Token获取成功"
		result["token_preview"] = token[:10] + "..." // Show first 10 chars
	}

	c.JSON(http.StatusOK, result)
}

// SimulateIncomingMessage simulates an incoming message for testing
func (h *DingTalkHandler) SimulateIncomingMessage(c *gin.Context) {
	var request struct {
		SenderID         string `json:"sender_id" binding:"required"`
		SenderName       string `json:"sender_name" binding:"required"`
		ConversationID   string `json:"conversation_id"`
		ConversationType string `json:"conversation_type" binding:"required"` // "1" for 1v1, "2" for group
		MessageContent   string `json:"message_content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 如果没有提供conversationID，使用senderID
	if request.ConversationID == "" {
		request.ConversationID = request.SenderID
	}

	// 处理消息
	err := h.dingTalkService.HandleIncomingMessage(
		request.SenderID,
		request.SenderName,
		request.ConversationID,
		request.ConversationType,
		request.MessageContent,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "消息处理成功",
		"sender":  request.SenderName,
		"type":    request.ConversationType,
	})
}

// GetCollectedIDs returns collected user and group IDs
func (h *DingTalkHandler) GetCollectedIDs(c *gin.Context) {
	// 读取ID配置文件
	filename := "dingtalk_ids.txt"
	file, err := os.Open(filename)
	if err != nil {
		if os.IsNotExist(err) {
			c.JSON(http.StatusOK, gin.H{
				"users":  []map[string]string{},
				"groups": []map[string]string{},
				"total":  0,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer file.Close()

	var users []map[string]string
	var groups []map[string]string

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, ":", 2)
		if len(parts) == 2 {
			id := strings.TrimSpace(parts[0])
			name := strings.TrimSpace(parts[1])

			// 简单判断是用户还是群组
			if len(id) < 30 {
				users = append(users, map[string]string{"id": id, "name": name})
			} else {
				groups = append(groups, map[string]string{"id": id, "name": name})
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"users":  users,
		"groups": groups,
		"total":  len(users) + len(groups),
	})
}

// HandleWebhook handles incoming webhook messages from DingTalk
func (h *DingTalkHandler) HandleWebhook(c *gin.Context) {
	var webhookData map[string]interface{}

	if err := c.ShouldBindJSON(&webhookData); err != nil {
		log.Printf("❌ Failed to parse webhook data: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON"})
		return
	}

	log.Printf("📱 收到钉钉Webhook消息: %+v", webhookData)

	// 解析消息内容
	// 这里需要根据钉钉的实际Webhook格式来解析
	// 暂时返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"message": "Webhook received",
		"status":  "success",
	})
}

// Helper function to get records by IDs
func (h *DingTalkHandler) getRecordsByIDs(recordIDs []int) ([]services.TrendsRecord, error) {
	return h.database.GetTrendsRecordsByIDs(recordIDs)
}
