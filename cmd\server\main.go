package main

import (
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/handlers"
	"go_lilybot_api_trends/internal/services"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("🚀 Starting Go LilyBot API Trends...")

	// Load configuration
	log.Println("📋 Loading configuration...")
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}
	log.Printf("✅ Configuration loaded successfully. Port: %s", cfg.Port)

	// Set Gin mode
	gin.SetMode(cfg.GinMode)
	log.Printf("🔧 Gin mode set to: %s", cfg.GinMode)

	// Initialize services
	log.Println("🔧 Initializing services...")
	serpApiService := services.NewSerpApiService(cfg.SerpApi<PERSON>ey, cfg)
	analyzer := services.NewTrendsAnalyzer()

	// Initialize database service
	var database *services.DatabaseService
	if cfg.Database.Enabled {
		db, err := services.NewDatabaseService(cfg.Database.Path)
		if err != nil {
			log.Printf("⚠️  Failed to initialize database: %v", err)
			log.Println("📊 Continuing without database - data will only be stored in memory")
		} else {
			database = db
			log.Printf("✅ Database initialized: %s", cfg.Database.Path)
		}
	}

	// Initialize DingTalk service
	var dingTalkService *services.DingTalkService
	dingTalkConfig, err := config.LoadDingTalkConfig()
	if err != nil {
		log.Printf("⚠️  Failed to load DingTalk configuration: %v", err)
		log.Println("📱 Continuing without DingTalk integration")
	} else {
		dingTalkService, err = services.NewDingTalkService(dingTalkConfig, database)
		if err != nil {
			log.Printf("⚠️  Failed to initialize DingTalk service: %v", err)
			log.Println("📱 Continuing without DingTalk integration")
		} else {
			log.Println("✅ DingTalk service initialized")
		}
	}

	// Initialize scheduler service with DingTalk service
	scheduler := services.NewSchedulerService(serpApiService, cfg, database, dingTalkService)

	// Start scheduler if enabled
	if cfg.Scheduler.Enabled {
		if err := scheduler.Start(); err != nil {
			log.Printf("⚠️  Failed to start scheduler: %v", err)
		}
	}

	// Initialize data maintenance service
	var dataMaintenanceService *services.DataMaintenanceService
	if database != nil {
		dataMaintenanceService = services.NewDataMaintenanceService(&cfg.DataMaintenance, database)

		// Start data maintenance scheduler if enabled
		if cfg.DataMaintenance.Enabled {
			if err := dataMaintenanceService.Start(); err != nil {
				log.Printf("⚠️  Failed to start data maintenance scheduler: %v", err)
			}
		}
	}

	// Initialize Windows keep-alive service
	keepAliveService := services.NewWindowsKeepAliveService(&cfg.KeepAlive)
	if err := keepAliveService.Start(); err != nil {
		log.Printf("⚠️  Failed to start Windows keep-alive service: %v", err)
	}

	log.Println("✅ Services initialized")

	// Initialize handlers
	log.Println("🔧 Initializing handlers...")
	trendsHandler := handlers.NewTrendsHandler(serpApiService, analyzer, cfg)
	adminHandler := handlers.NewAdminHandler(cfg, serpApiService, scheduler, database, dataMaintenanceService, keepAliveService)

	var dingTalkHandler *handlers.DingTalkHandler
	var webhookHandler *handlers.DingTalkWebhookHandler
	if dingTalkService != nil && database != nil {
		dingTalkHandler = handlers.NewDingTalkHandler(dingTalkService, database)
		webhookHandler = handlers.NewDingTalkWebhookHandler(cfg, dingTalkService)
		log.Println("✅ DingTalk handlers initialized")

		// 启动消息监听器
		log.Println("🔧 Starting DingTalk message listener...")
		err := dingTalkService.StartMessageListener()
		if err != nil {
			log.Printf("⚠️  Failed to start DingTalk message listener: %v", err)
		} else {
			log.Println("✅ DingTalk message listener started")
		}
	}

	log.Println("✅ Handlers initialized")

	// Setup router
	log.Println("🔧 Setting up router...")
	router := setupRouter(trendsHandler, adminHandler, dingTalkHandler, webhookHandler)
	log.Println("✅ Router setup complete")

	// Start server
	log.Printf("🚀 Starting server on port %s", cfg.Port)
	log.Printf("🌐 Server running at http://localhost:%s", cfg.Port)
	log.Printf("📚 API documentation available at http://localhost:%s/docs", cfg.Port)
	log.Printf("❤️  Health check available at http://localhost:%s/health", cfg.Port)
	log.Println("💡 按 Ctrl+C 停止服务器")

	// 设置信号处理
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	// 在goroutine中启动服务器
	go func() {
		if err := router.Run(cfg.GetServerAddress()); err != nil {
			log.Fatalf("❌ Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	<-c
	log.Println("\n🛑 收到停止信号，正在关闭服务器...")

	// Stop Windows keep-alive service
	if keepAliveService != nil {
		keepAliveService.Stop()
	}

	log.Println("✅ 服务器已停止")
}

func setupRouter(trendsHandler *handlers.TrendsHandler, adminHandler *handlers.AdminHandler, dingTalkHandler *handlers.DingTalkHandler, webhookHandler *handlers.DingTalkWebhookHandler) *gin.Engine {
	router := gin.Default()

	// Add CORS middleware
	router.Use(corsMiddleware())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "go_lilybot_api_trends",
			"version": "1.0.0",
		})
	})

	// API documentation endpoint
	router.GET("/docs", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "Google Trends API for LilyBot",
			"version": "1.0.0",
			"endpoints": gin.H{
				"health": "GET /health - Health check",
				"trends": gin.H{
					"general":            "GET /api/trends - General trends data",
					"interest_over_time": "GET /api/trends/interest-over-time - Interest over time",
					"interest_by_region": "GET /api/trends/interest-by-region - Interest by region",
					"related_topics":     "GET /api/trends/related-topics - Related topics",
					"related_queries":    "GET /api/trends/related-queries - Related queries",
					"compare":            "GET /api/trends/compare - Compare multiple queries",
				},
			},
			"parameters": gin.H{
				"query":                     "Search query (required for most endpoints)",
				"queries":                   "Comma-separated queries for comparison (2-5 queries)",
				"data_type":                 "TIMESERIES, GEO_MAP_0, RELATED_TOPICS, RELATED_QUERIES, GEO_MAP",
				"geo":                       "Geographic location code (e.g., US, GB, JP)",
				"date":                      "Date range (e.g., 'today 12-m', 'now 7-d', '2023-01-01 2023-12-31')",
				"language":                  "Language code (e.g., en, es, fr)",
				"timezone":                  "Timezone offset in minutes (-1439 to 1439)",
				"category":                  "Search category ID",
				"property":                  "Search property (images, news, froogle, youtube)",
				"include_low_search_volume": "Include low search volume regions (true/false)",
				"analyze":                   "Include AI analysis (true/false)",
			},
			"examples": gin.H{
				"basic_search":      "/api/trends?query=coffee",
				"regional_interest": "/api/trends/interest-by-region?query=pizza&geo=US",
				"compare_queries":   "/api/trends/compare?queries=coffee,tea,chocolate",
				"with_analysis":     "/api/trends?query=artificial intelligence&analyze=true",
				"time_range":        "/api/trends?query=christmas&date=today 12-m",
			},
		})
	})

	// API routes
	api := router.Group("/api")
	{
		trends := api.Group("/trends")
		{
			trends.GET("", trendsHandler.GetTrends)
			trends.GET("/interest-over-time", trendsHandler.GetInterestOverTime)
			trends.GET("/interest-by-region", trendsHandler.GetInterestByRegion)
			trends.GET("/related-topics", trendsHandler.GetRelatedTopics)
			trends.GET("/related-queries", trendsHandler.GetRelatedQueries)
			trends.GET("/compare", trendsHandler.CompareQueries)
			trends.GET("/explore", trendsHandler.GetTrendsExplore)
			trends.GET("/trending-now", trendsHandler.GetTrendingNow)
		}

		// Admin API routes
		if adminHandler != nil {
			admin := api.Group("/admin")
			{
				admin.GET("/task-groups", adminHandler.GetTaskGroups)
				admin.GET("/database-task-groups", adminHandler.GetDatabaseTaskGroups)
				admin.POST("/execute/:name", adminHandler.ExecuteTaskGroup)
				admin.GET("/results", adminHandler.GetResults)
				admin.GET("/stats", adminHandler.GetDatabaseStats)
				admin.GET("/history", adminHandler.GetTrendsHistory)
				admin.GET("/workday-info", adminHandler.GetWorkdayInfo)
				admin.DELETE("/records/:id", adminHandler.DeleteTrendsRecord)
				admin.DELETE("/records", adminHandler.DeleteTrendsRecords)
				admin.GET("/records/:id/details", adminHandler.GetRecordDetails)
				admin.GET("/export", adminHandler.ExportTrendsData)

				// Data maintenance routes
				admin.GET("/data-stats", adminHandler.GetDataStats)
				admin.POST("/cleanup", adminHandler.ManualCleanup)

				// DingTalk routes
				if dingTalkHandler != nil {
					dingtalk := admin.Group("/dingtalk")
					{
						dingtalk.GET("/config", dingTalkHandler.GetConfig)
						dingtalk.GET("/recipients", dingTalkHandler.GetRecipients)
						dingtalk.POST("/recipients", dingTalkHandler.AddRecipient)
						dingtalk.POST("/push", dingTalkHandler.SendManualPush)
						dingtalk.POST("/test", dingTalkHandler.SendTestMessage)
						dingtalk.POST("/detect-user", dingTalkHandler.DetectUserID)
						dingtalk.POST("/test-connection", dingTalkHandler.TestConnection)
						dingtalk.GET("/unpushed", dingTalkHandler.GetUnpushedRecords)
						dingtalk.POST("/preview", dingTalkHandler.PreviewMessage)
						dingtalk.POST("/simulate", dingTalkHandler.SimulateIncomingMessage)
						dingtalk.GET("/collected-ids", dingTalkHandler.GetCollectedIDs)
					}
				}
			}
		}
	}

	// 钉钉Webhook路由 (公开访问，不需要认证)
	if webhookHandler != nil {
		api.POST("/dingtalk/webhook", webhookHandler.HandleWebhook)
		log.Println("✅ DingTalk webhook endpoint registered at /api/dingtalk/webhook")
	}

	// Admin interface routes
	if adminHandler != nil {
		router.GET("/admin", adminHandler.GetAdminDashboard)
	}

	return router
}

// corsMiddleware adds CORS headers
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
