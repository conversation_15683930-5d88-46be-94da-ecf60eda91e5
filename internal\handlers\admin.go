package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/services"

	"github.com/gin-gonic/gin"
)

// AdminHandler handles admin interface requests
type AdminHandler struct {
	config                 *config.Config
	serpApiService         *services.SerpApiService
	scheduler              *services.SchedulerService
	database               *services.DatabaseService
	dataMaintenanceService *services.DataMaintenanceService
	keepAliveService       *services.WindowsKeepAliveService
}

// NewAdminHandler creates a new admin handler
func NewAdminHandler(cfg *config.Config, serpApiService *services.SerpApiService, scheduler *services.SchedulerService, database *services.DatabaseService, dataMaintenanceService *services.DataMaintenanceService, keepAliveService *services.WindowsKeepAliveService) *AdminHandler {
	return &AdminHandler{
		config:                 cfg,
		serpApiService:         serpApiService,
		scheduler:              scheduler,
		database:               database,
		dataMaintenanceService: dataMaintenanceService,
		keepAliveService:       keepAliveService,
	}
}

// GetAdminDashboard serves the admin dashboard
func (h *AdminHandler) GetAdminDashboard(c *gin.Context) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LilyBot API Trends - 管理面板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; min-width: 100px; text-align: center; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.enabled { background: #d4edda; color: #155724; }
        .status.disabled { background: #f8d7da; color: #721c24; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: 600; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 14px; max-height: 300px; overflow-y: auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: 600; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 LilyBot API Trends 管理面板</h1>
            <p>Google Trends 数据采集与分析系统</p>
        </div>

        <div class="grid">
            <div class="card">
                <h2>📊 系统状态</h2>
                <table>
                    <tr><td>服务端口</td><td>` + h.config.Port + `</td></tr>
                    <tr><td>运行模式</td><td>` + h.config.GinMode + `</td></tr>
                    <tr><td>定时任务</td><td><span class="status ` + getStatusClass(h.config.Scheduler.Enabled) + `">` + getStatusText(h.config.Scheduler.Enabled) + `</span></td></tr>
                    <tr><td>任务组</td><td><span class="status ` + getStatusClass(h.config.TaskGroupsEnabled) + `">` + getStatusText(h.config.TaskGroupsEnabled) + `</span></td></tr>
                    <tr><td>执行时间</td><td>` + h.config.Scheduler.Time + ` (` + h.config.Scheduler.Timezone + `)</td></tr>
                    <tr><td>工作日模式</td><td>` + getWorkdayModeText(h.config.Scheduler.WorkdaysOnly, h.config.Scheduler.WorkdayPattern) + `</td></tr>
                    <tr><td>Windows保活</td><td><span class="status ` + getStatusClass(h.config.KeepAlive.Enabled) + `">` + getStatusText(h.config.KeepAlive.Enabled) + `</span> ` + getKeepAliveInfo(h.keepAliveService) + `</td></tr>
                </table>
            </div>

            <div class="card">
                <h2>⚙️ 快速操作</h2>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="btn" onclick="executeTaskGroup('China_Explore')">中国探索</button>
                    <button class="btn" onclick="executeTaskGroup('US_Explore')">美国探索</button>
                    <button class="btn" onclick="executeTaskGroup('HongKong_Trending_Now')">香港实时</button>
                    <button class="btn" onclick="executeTaskGroup('US_Trending_Now')">美国实时</button>
                    <button class="btn btn-warning" onclick="showDataManager()">数据管理</button>
                    <button class="btn btn-warning" onclick="showDingTalkManager()">钉钉推送</button>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📋 任务组配置</h2>
            <table>
                <thead>
                    <tr><th>名称</th><th>状态</th><th>地区</th><th>时间范围</th><th>查询词数量</th><th>操作</th></tr>
                </thead>
                <tbody id="taskGroupsTable">
                    <!-- 任务组数据将通过JavaScript加载 -->
                </tbody>
            </table>
        </div>

        <div class="card">
            <h2>🔍 手动查询</h2>
            <form onsubmit="manualQuery(event)">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: end;">
                    <div class="form-group">
                        <label>查询词</label>
                        <input type="text" id="queryInput" placeholder="例如：人工智能" required>
                    </div>
                    <div class="form-group">
                        <label>地区</label>
                        <select id="geoInput">
                            <option value="">全球</option>
                            <option value="CN">中国</option>
                            <option value="US">美国</option>
                            <option value="JP">日本</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>时间范围</label>
                        <select id="dateInput">
                            <option value="today 7-d">近7天</option>
                            <option value="today 1-m">近1个月</option>
                            <option value="today 3-m">近3个月</option>
                            <option value="today 12-m">近12个月</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">查询</button>
                </div>
            </form>
        </div>

        <!-- 最新结果区域已移除，使用查看数据按钮的弹窗功能 -->

        <div class="card">
            <h2>📝 系统日志</h2>
            <div class="log" id="logContainer">
                <p>日志加载中...</p>
            </div>
        </div>
    </div>

    <script>
        // 加载任务组数据
        async function loadTaskGroups() {
            try {
                const response = await fetch('/api/admin/task-groups');
                const data = await response.json();
                const tbody = document.getElementById('taskGroupsTable');
                tbody.innerHTML = data.map(group => {
                    // 根据数据类型显示不同的时间范围信息
                    let timeRange = '';
                    if (group.DataType === 'EXPLORE') {
                        timeRange = '探索模式 (' + (group.ExploreDataType || 'RELATED_TOPICS') + ')';
                    } else if (group.DataType === 'TRENDING_NOW') {
                        timeRange = (group.Hours || 168) + '小时';
                    } else if (group.DataType === 'TIMESERIES') {
                        timeRange = group.Date || 'now 7-d';
                    }

                    // 查询词数量
                    const queryCount = group.Queries ? group.Queries.length : 0;

                    // 工作日模式显示
                    const workdayInfo = group.WorkdaysOnly ?
                        ' 🗓️ ' + (group.WorkdayPattern || 'MON-FRI') : '';

                    return '<tr>' +
                        '<td>' + group.Name + workdayInfo + '</td>' +
                        '<td><span class="status ' + (group.Enabled ? 'enabled' : 'disabled') + '">' + (group.Enabled ? '启用' : '禁用') + '</span></td>' +
                        '<td>' + (group.Geo || '全球') + '</td>' +
                        '<td>' + timeRange + '</td>' +
                        '<td>' + queryCount + '</td>' +
                        '<td><button class="btn" onclick="executeTaskGroup(\'' + group.Name + '\')">执行</button></td>' +
                        '</tr>';
                }).join('');
            } catch (error) {
                console.error('Failed to load task groups:', error);
                document.getElementById('taskGroupsTable').innerHTML =
                    '<tr><td colspan="6">加载任务组失败: ' + error.message + '</td></tr>';
            }
        }

        // 执行任务组
        async function executeTaskGroup(name) {
            try {
                const response = await fetch('/api/admin/execute/' + name, { method: 'POST' });
                const result = await response.json();
                alert(result.message || '任务执行完成');
                loadResults();
            } catch (error) {
                alert('执行失败: ' + error.message);
            }
        }

        // 测试API连接
        async function testApi() {
            try {
                const response = await fetch('/api/trends/interest-over-time?query=test&geo=CN&date=now 7-d');
                if (response.ok) {
                    alert('API连接正常 ✅');
                } else {
                    alert('API连接异常 ❌');
                }
            } catch (error) {
                alert('API测试失败: ' + error.message);
            }
        }

        // 测试探索API (使用配置文件的默认值)
        async function testExploreApi() {
            try {
                const response = await fetch('/api/trends/explore');
                if (response.ok) {
                    const data = await response.json();
                    alert('探索API正常 ✅\\n成功获取探索数据\\n使用配置文件默认参数');
                } else {
                    alert('探索API异常 ❌');
                }
            } catch (error) {
                alert('探索API测试失败: ' + error.message);
            }
        }

        // 测试热门搜索API (使用配置文件的默认值)
        async function testTrendingNow() {
            try {
                const response = await fetch('/api/trends/trending-now');
                if (response.ok) {
                    const data = await response.json();
                    const count = data.trending_searches ? data.trending_searches.length : 0;
                    alert('热门搜索API正常 ✅\\n找到 ' + count + ' 个热门搜索\\n使用配置文件默认参数');
                } else {
                    alert('热门搜索API异常 ❌');
                }
            } catch (error) {
                alert('热门搜索API测试失败: ' + error.message);
            }
        }



        // 执行任务组
        async function executeTaskGroup(taskGroupName) {
            if (!confirm('确定要执行任务组 "' + taskGroupName + '" 吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/admin/execute/' + taskGroupName, {
                    method: 'POST'
                });
                const result = await response.json();

                if (response.ok) {
                    alert('✅ 任务组执行成功: ' + result.message);
                    // 刷新任务组列表
                    loadTaskGroups();
                } else {
                    alert('❌ 任务组执行失败: ' + result.error);
                }
            } catch (error) {
                alert('❌ 任务组执行失败: ' + error.message);
            }
        }



        // 关闭结果弹窗
        function closeResultsModal() {
            const modals = document.querySelectorAll('div[style*="z-index: 1000"]');
            modals.forEach(modal => {
                document.body.removeChild(modal);
            });
        }

        // 显示数据管理界面
        function showDataManager() {
            const modal = document.createElement('div');
            modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;';

            modal.innerHTML = '<div style="background: white; padding: 20px; border-radius: 8px; width: 80%; max-width: 800px; max-height: 80%; overflow-y: auto; position: relative;">' +
                '<button onclick="closeDataManager()" style="position: absolute; top: 10px; right: 15px; background: none; border: none; font-size: 20px; cursor: pointer; color: #999; padding: 5px; line-height: 1;" title="关闭">×</button>' +
                '<h3>📊 数据管理</h3>' +
                '<div style="margin-bottom: 20px;">' +
                '<h4>🔍 筛选条件</h4>' +
                '<div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">' +
                '<select id="filterTaskGroup"><option value="">所有任务组</option><option value="China_Explore">中国探索</option><option value="US_Explore">美国探索</option><option value="HongKong_Trending_Now">香港实时</option><option value="US_Trending_Now">美国实时</option></select>' +
                '<select id="filterDataType"><option value="">所有类型</option><option value="EXPLORE">探索模式</option><option value="TRENDING_NOW">实时热门</option><option value="TIMESERIES">时间序列</option></select>' +
                '<select id="filterGeo"><option value="">所有地区</option><option value="CN">中国</option><option value="US">美国</option><option value="HK">香港</option></select>' +
                '<input type="number" id="filterDays" placeholder="天数" value="30" min="1" max="365">' +
                '</div>' +
                '<div style="display: flex; gap: 10px;">' +
                '<button onclick="loadDataHistory()" class="btn">🔍 查询数据</button>' +
                '<button onclick="exportData(\'csv\')" class="btn btn-success">📤 导出CSV</button>' +
                '<button onclick="exportData(\'json\')" class="btn btn-success">📤 导出JSON</button>' +
                '<button onclick="deleteSelectedRecords()" class="btn btn-danger">🗑️ 删除选中</button>' +
                '</div>' +
                '</div>' +
                '<div id="dataTableContainer"><p>点击"查询数据"开始...</p></div>' +
                '</div>';

            document.body.appendChild(modal);
            loadDataHistory(); // 自动加载数据
        }





        // 手动查询
        async function manualQuery(event) {
            event.preventDefault();
            const query = document.getElementById('queryInput').value;
            const geo = document.getElementById('geoInput').value;
            const date = document.getElementById('dateInput').value;
            
            try {
                const url = '/api/trends/interest-over-time?query=' + encodeURIComponent(query) + 
                           '&geo=' + geo + '&date=' + encodeURIComponent(date) + '&analyze=true';
                const response = await fetch(url);
                const data = await response.json();
                
                alert('查询完成！平均搜索热度: ' + (data.analysis?.average_interest || 'N/A'));
                loadResults();
            } catch (error) {
                alert('查询失败: ' + error.message);
            }
        }

        // 加载数据历史
        async function loadDataHistory() {
            const taskGroup = document.getElementById('filterTaskGroup')?.value || '';
            const dataType = document.getElementById('filterDataType')?.value || '';
            const geo = document.getElementById('filterGeo')?.value || '';
            const days = document.getElementById('filterDays')?.value || '30';

            const params = new URLSearchParams();
            if (taskGroup) params.append('task_group', taskGroup);
            if (dataType) params.append('data_type', dataType);
            if (geo) params.append('geo', geo);
            if (days) params.append('days', days);

            try {
                const response = await fetch('/api/admin/history?' + params.toString());
                const data = await response.json();

                const container = document.getElementById('dataTableContainer');
                if (!container) return;

                if (data.length === 0) {
                    container.innerHTML = '<p>❌ 没有找到匹配的数据</p>';
                    return;
                }

                let html = '<table class="table"><thead><tr>';
                html += '<th><input type="checkbox" onchange="toggleAllCheckboxes(this)"></th>';
                html += '<th>ID</th><th>任务组</th><th>查询词</th><th>地区</th><th>类型</th>';
                html += '<th>执行时间</th><th>兴趣度</th><th>数据点</th><th>操作</th>';
                html += '</tr></thead><tbody>';

                // Group records by batch_id for better display
                const groupedRecords = {};
                const standaloneRecords = [];

                data.forEach(record => {
                    if (record.batch_id) {
                        if (!groupedRecords[record.batch_id]) {
                            groupedRecords[record.batch_id] = {
                                main: null,
                                children: []
                            };
                        }
                        // Main record: has batch_id but no parent_task_id (or parent_task_id is empty)
                        if (!record.parent_task_id || record.parent_task_id === '') {
                            groupedRecords[record.batch_id].main = record;
                        } else {
                            // Child record: has both batch_id and parent_task_id
                            groupedRecords[record.batch_id].children.push(record);
                        }
                    } else {
                        standaloneRecords.push(record);
                    }
                });

                // Display grouped records (batches)
                Object.keys(groupedRecords).forEach(batchId => {
                    const batch = groupedRecords[batchId];
                    if (batch.main) {
                        // Main record
                        html += '<tr style="background-color: #f8f9fa; font-weight: bold;">';
                        html += '<td><input type="checkbox" class="record-checkbox" value="' + batch.main.id + '"></td>';
                        html += '<td>' + batch.main.id + '</td>';
                        html += '<td>' + batch.main.task_group + '</td>';
                        html += '<td>📦 ' + batch.main.query + ' (' + batch.children.length + ' 项)</td>';
                        html += '<td>' + batch.main.geo + '</td>';
                        html += '<td>' + batch.main.data_type + '</td>';
                        html += '<td>' + new Date(batch.main.executed_at).toLocaleString() + '</td>';
                        html += '<td>' + batch.main.avg_interest + '</td>';
                        html += '<td>' + batch.main.data_points + '</td>';
                        html += '<td>';
                        html += '<button onclick="viewRecordDetails(' + batch.main.id + ')" class="btn btn-sm btn-info" style="margin-right: 5px;">详情</button>';
                        html += '<button onclick="deleteRecord(' + batch.main.id + ')" class="btn btn-sm btn-danger">删除</button>';
                        html += '</td>';
                        html += '</tr>';

                        // 不再显示子记录，用户可以通过点击详情按钮查看
                    }
                });

                // Display standalone records
                standaloneRecords.forEach(record => {
                    html += '<tr>';
                    html += '<td><input type="checkbox" class="record-checkbox" value="' + record.id + '"></td>';
                    html += '<td>' + record.id + '</td>';
                    html += '<td>' + record.task_group + '</td>';
                    html += '<td>' + record.query + '</td>';
                    html += '<td>' + record.geo + '</td>';
                    html += '<td>' + record.data_type + '</td>';
                    html += '<td>' + new Date(record.executed_at).toLocaleString() + '</td>';
                    html += '<td>' + record.avg_interest + '</td>';
                    html += '<td>' + record.data_points + '</td>';
                    html += '<td>';
                    html += '<button onclick="viewRecordDetails(' + record.id + ')" class="btn btn-sm btn-info" style="margin-right: 5px;">详情</button>';
                    html += '<button onclick="deleteRecord(' + record.id + ')" class="btn btn-sm btn-danger">删除</button>';
                    html += '</td>';
                    html += '</tr>';
                });

                html += '</tbody></table>';
                container.innerHTML = html;

            } catch (error) {
                const container = document.getElementById('dataTableContainer');
                if (container) {
                    container.innerHTML = '<p>❌ 加载数据失败: ' + error.message + '</p>';
                }
            }
        }

        // 切换所有复选框
        function toggleAllCheckboxes(source) {
            const checkboxes = document.querySelectorAll('.record-checkbox');
            checkboxes.forEach(cb => cb.checked = source.checked);
        }

        // 删除单个记录
        async function deleteRecord(id) {
            if (!confirm('确定要删除这条记录吗？')) return;

            try {
                const response = await fetch('/api/admin/records/' + id, { method: 'DELETE' });
                const result = await response.json();

                if (response.ok) {
                    alert('✅ 删除成功');
                    loadDataHistory();
                } else {
                    alert('❌ 删除失败: ' + result.error);
                }
            } catch (error) {
                alert('❌ 删除失败: ' + error.message);
            }
        }

        // 删除选中的记录
        async function deleteSelectedRecords() {
            const checkboxes = document.querySelectorAll('.record-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要删除的记录');
                return;
            }

            if (!confirm('确定要删除选中的 ' + checkboxes.length + ' 条记录吗？')) return;

            const ids = Array.from(checkboxes).map(cb => parseInt(cb.value));

            try {
                const response = await fetch('/api/admin/records', {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: ids })
                });
                const result = await response.json();

                if (response.ok) {
                    alert('✅ 成功删除 ' + result.deleted + ' 条记录');
                    loadDataHistory();
                } else {
                    alert('❌ 删除失败: ' + result.error);
                }
            } catch (error) {
                alert('❌ 删除失败: ' + error.message);
            }
        }

        // 导出数据
        function exportData(format) {
            const taskGroup = document.getElementById('filterTaskGroup')?.value || '';
            const dataType = document.getElementById('filterDataType')?.value || '';
            const geo = document.getElementById('filterGeo')?.value || '';
            const days = document.getElementById('filterDays')?.value || '30';

            const params = new URLSearchParams();
            params.append('format', format);
            if (taskGroup) params.append('task_group', taskGroup);
            if (dataType) params.append('data_type', dataType);
            if (geo) params.append('geo', geo);
            if (days) params.append('days', days);

            window.open('/api/admin/export?' + params.toString(), '_blank');
        }

        // 查看记录详情
        async function viewRecordDetails(id) {
            try {
                const response = await fetch('/api/admin/records/' + id + '/details');
                const data = await response.json();

                if (!response.ok) {
                    alert('❌ 获取详情失败: ' + data.error);
                    return;
                }

                // 创建详情弹窗
                const modal = document.createElement('div');
                modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1001; display: flex; align-items: center; justify-content: center;';

                let detailsHtml = '<div style="background: white; padding: 20px; border-radius: 8px; width: 95%; max-width: 1200px; max-height: 85%; overflow-y: auto;">';

                // 检查是否有主记录和子记录
                if (data.main_record) {
                    const record = data.main_record;
                    detailsHtml += '<h3>📊 ' + record.task_group + ' - ' + record.query + '</h3>';

                    if (data.total_children > 0) {
                        detailsHtml += '<p style="color: #28a745; font-weight: bold;">📦 获取时间: ' + new Date(record.executed_at).toLocaleString() + ' | 包含 ' + data.total_children + ' 个数据</p>';
                    } else {
                        detailsHtml += '<p style="color: #666;">📦 获取时间: ' + new Date(record.executed_at).toLocaleString() + '</p>';
                    }
                } else {
                    // 兼容旧格式
                    const record = data;
                    detailsHtml += '<h3>📊 ' + record.task_group + ' - ' + record.query + '</h3>';
                    detailsHtml += '<p style="color: #666;">📦 获取时间: ' + new Date(record.executed_at).toLocaleString() + '</p>';
                }

                const record = data.main_record || data;

                // 直接显示子记录，不显示原始数据

                // 显示所有数据（如果有的话）
                if (data.child_records && data.child_records.length > 0) {
                    detailsHtml += '<div style="margin-bottom: 20px;">';
                    detailsHtml += '<h4>📊 所有数据 (' + data.child_records.length + ' 个)</h4>';
                    detailsHtml += '<div style="max-height: 500px; overflow-y: auto;">';

                    data.child_records.forEach((child, index) => {
                        detailsHtml += '<div style="padding: 12px; margin-bottom: 8px; border: 1px solid #e0e0e0; border-radius: 4px; ' + (index % 2 === 0 ? 'background-color: #f8f9fa;' : 'background-color: white;') + '">';
                        detailsHtml += '<h5 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">🔍 ' + child.query + '</h5>';
                        detailsHtml += '<p style="margin: 0; color: #666; font-size: 14px;">获取时间: ' + new Date(child.executed_at).toLocaleString() + '</p>';
                        detailsHtml += '</div>';
                    });

                    detailsHtml += '</div>';
                    detailsHtml += '</div>';
                }

                detailsHtml += '<div style="text-align: right;">';
                detailsHtml += '<button onclick="closeDetailsModal()" class="btn">关闭</button>';
                detailsHtml += '</div>';
                detailsHtml += '</div>';

                modal.innerHTML = detailsHtml;
                document.body.appendChild(modal);

            } catch (error) {
                alert('❌ 获取详情失败: ' + error.message);
            }
        }

        // 关闭详情弹窗
        function closeDetailsModal() {
            const modals = document.querySelectorAll('div[style*="z-index: 1001"]');
            modals.forEach(modal => {
                document.body.removeChild(modal);
            });
        }

        // 显示钉钉推送管理界面
        function showDingTalkManager() {
            const modal = document.createElement('div');
            modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;';

            modal.innerHTML = '<div style="background: white; padding: 20px; border-radius: 8px; width: 95%; max-width: 1200px; max-height: 85%; overflow-y: auto; position: relative;">' +
                '<button onclick="closeDingTalkManager()" style="position: absolute; top: 10px; right: 15px; background: none; border: none; font-size: 20px; cursor: pointer; color: #999; padding: 5px; line-height: 1;" title="关闭">×</button>' +
                '<h3>📱 钉钉推送管理</h3>' +

                // 第一种：发送自定义消息
                '<div style="margin-bottom: 25px; border: 1px solid #e0e0e0; padding: 15px; border-radius: 8px;">' +
                '<h4>📝 第一种：发送自定义消息</h4>' +
                '<p style="color: #666; margin-bottom: 10px;">手动输入消息内容并选择推送目标</p>' +
                '<div style="margin-bottom: 15px;">' +
                '<textarea id="testMessage" placeholder="输入要发送的消息..." style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;">这是一条来自LilyBot的测试消息 🤖</textarea>' +
                '</div>' +
                '<div style="margin-bottom: 15px;">' +
                '<h5>📋 选择推送目标：</h5>' +
                '<div id="recipientCheckboxes" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;"></div>' +
                '</div>' +
                '<div style="display: flex; gap: 10px; flex-wrap: wrap;">' +
                '<button onclick="selectAllRecipients(true)" class="btn btn-sm">全选</button>' +
                '<button onclick="selectAllRecipients(false)" class="btn btn-sm">取消全选</button>' +
                '<button onclick="sendCustomMessageToSelected()" class="btn btn-success">📤 发送给选中目标</button>' +
                '<button onclick="sendTestMessageToAll()" class="btn btn-warning">📤 发送给所有人</button>' +
                '</div>' +
                '</div>' +

                // 第二种：推送数据库消息
                '<div style="margin-bottom: 25px; border: 1px solid #e0e0e0; padding: 15px; border-radius: 8px;">' +
                '<h4>📊 第二种：推送数据库里的消息</h4>' +
                '<p style="color: #666; margin-bottom: 10px;">从数据库中选择已有的数据记录进行推送（支持多选任务组）</p>' +
                '<div style="margin-bottom: 15px;">' +
                '<h5>📋 选择任务组：</h5>' +
                '<div id="dbTaskGroupCheckboxes" style="max-height: 120px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;"></div>' +
                '</div>' +
                '<div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">' +
                '<button onclick="selectAllDbTaskGroups(true)" class="btn btn-sm">全选</button>' +
                '<button onclick="selectAllDbTaskGroups(false)" class="btn btn-sm">取消全选</button>' +
                '<button onclick="toggleDatabaseRecords()" class="btn" id="toggleDbRecordsBtn">🔍 查看数据库记录</button>' +
                '<button onclick="pushSelectedDbRecords()" class="btn btn-success">📤 推送选中记录给第一种选中目标</button>' +
                '<button onclick="pushSelectedDbRecordsToAll()" class="btn btn-warning">📤 推送给所有人</button>' +
                '</div>' +
                '<div id="databaseRecordsContainer" style="display: none;"><p>加载中...</p></div>' +
                '</div>' +

                // 第三种：按任务推送
                '<div style="margin-bottom: 25px; border: 1px solid #e0e0e0; padding: 15px; border-radius: 8px;">' +
                '<h4>🎯 第三种：按任务推送消息</h4>' +
                '<p style="color: #666; margin-bottom: 10px;">选择特定任务组，直接爬取当前数据并推送（支持多选）</p>' +
                '<div style="margin-bottom: 15px;">' +
                '<h5>📋 选择任务组：</h5>' +
                '<div id="taskGroupCheckboxes" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;"></div>' +
                '</div>' +
                '<div style="display: flex; gap: 10px; flex-wrap: wrap;">' +
                '<button onclick="selectAllTaskGroups(true)" class="btn btn-sm">全选</button>' +
                '<button onclick="selectAllTaskGroups(false)" class="btn btn-sm">取消全选</button>' +
                '<button onclick="pushTaskRecordsToJH()" class="btn btn-success">📤 推送给第一种选中目标</button>' +
                '<button onclick="pushTaskRecordsToAll()" class="btn btn-warning">📤 推送给所有人</button>' +
                '</div>' +
                '</div>' +

                '</div>';

            document.body.appendChild(modal);
            loadRecipientsForSelection(); // 自动加载接收者选择框
            loadDatabaseTaskGroupsForSelection(); // 自动加载数据库任务组选择框
            loadDatabaseTaskGroups(); // 自动加载数据库任务组
            loadConfigTaskGroups(); // 自动加载配置任务组
        }

        // 关闭钉钉推送管理弹窗
        function closeDingTalkManager() {
            const modals = document.querySelectorAll('div[style*="z-index: 1000"]');
            modals.forEach(modal => {
                document.body.removeChild(modal);
            });
        }



        // 加载接收者选择框
        async function loadRecipientsForSelection() {
            try {
                const response = await fetch('/api/admin/dingtalk/recipients');
                const recipients = await response.json();

                const container = document.getElementById('recipientCheckboxes');
                if (!container) return;

                // 清空现有内容
                container.innerHTML = '';

                if (recipients.length === 0) {
                    container.innerHTML = '<p>❌ 暂无接收者</p>';
                    return;
                }

                // 按类型分组
                const users = recipients.filter(r => r.recipient_type === 'user');
                const groups = recipients.filter(r => r.recipient_type === 'group');

                // 添加用户
                if (users.length > 0) {
                    const userHeader = document.createElement('div');
                    userHeader.innerHTML = '<strong>👤 用户：</strong>';
                    userHeader.style.marginBottom = '8px';
                    container.appendChild(userHeader);

                    users.forEach(user => {
                        const div = document.createElement('div');
                        div.style.marginBottom = '6px';
                        div.style.marginLeft = '10px';

                        const checkboxContainer = document.createElement('span');
                        checkboxContainer.style.display = 'inline-block';
                        checkboxContainer.style.width = '20px';
                        checkboxContainer.style.verticalAlign = 'top';

                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.id = 'user_' + user.recipient_id;
                        checkbox.value = 'user:' + user.recipient_id;
                        checkbox.className = 'recipient-checkbox';
                        checkbox.disabled = !user.enabled;

                        const label = document.createElement('label');
                        label.htmlFor = 'user_' + user.recipient_id;
                        label.textContent = user.name + ' (' + user.recipient_id + ')';
                        label.style.cursor = 'pointer';
                        label.style.display = 'inline-block';
                        label.style.verticalAlign = 'top';
                        if (!user.enabled) {
                            label.style.color = '#999';
                            label.style.textDecoration = 'line-through';
                        }

                        checkboxContainer.appendChild(checkbox);
                        div.appendChild(checkboxContainer);
                        div.appendChild(label);
                        container.appendChild(div);
                    });
                }

                // 添加群组
                if (groups.length > 0) {
                    const groupHeader = document.createElement('div');
                    groupHeader.innerHTML = '<strong>👥 群组：</strong>';
                    groupHeader.style.marginTop = '15px';
                    groupHeader.style.marginBottom = '8px';
                    container.appendChild(groupHeader);

                    groups.forEach(group => {
                        const div = document.createElement('div');
                        div.style.marginBottom = '6px';
                        div.style.marginLeft = '10px';

                        const checkboxContainer = document.createElement('span');
                        checkboxContainer.style.display = 'inline-block';
                        checkboxContainer.style.width = '20px';
                        checkboxContainer.style.verticalAlign = 'top';

                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.id = 'group_' + group.recipient_id;
                        checkbox.value = 'group:' + group.recipient_id;
                        checkbox.className = 'recipient-checkbox';
                        checkbox.disabled = !group.enabled;

                        const label = document.createElement('label');
                        label.htmlFor = 'group_' + group.recipient_id;
                        label.textContent = group.name + ' (' + group.recipient_id + ')';
                        label.style.cursor = 'pointer';
                        label.style.display = 'inline-block';
                        label.style.verticalAlign = 'top';
                        if (!group.enabled) {
                            label.style.color = '#999';
                            label.style.textDecoration = 'line-through';
                        }

                        checkboxContainer.appendChild(checkbox);
                        div.appendChild(checkboxContainer);
                        div.appendChild(label);
                        container.appendChild(div);
                    });
                }

            } catch (error) {
                console.error('加载接收者选择框失败:', error);
                const container = document.getElementById('recipientCheckboxes');
                if (container) {
                    container.innerHTML = '<p>❌ 加载接收者失败: ' + error.message + '</p>';
                }
            }
        }

        // 第一种：发送测试消息
        async function sendTestMessage() {
            const message = document.getElementById('testMessage')?.value || '这是一条测试消息';
            const testUserID = await getTestUserID();
            await sendCustomMessage(message, [testUserID]);
        }

        async function sendTestMessageToGroup() {
            const message = document.getElementById('testMessage')?.value || '这是一条测试消息';
            const testGroupID = await getTestGroupID();
            await sendCustomMessage(message, [testGroupID]);
        }

        async function sendTestMessageToAll() {
            const message = document.getElementById('testMessage')?.value || '这是一条测试消息';
            await sendCustomMessage(message, []); // 空数组表示发送给所有人
        }

        // 发送自定义消息给选中的接收者
        async function sendCustomMessageToSelected() {
            const message = document.getElementById('testMessage')?.value;
            if (!message || message.trim() === '') {
                alert('请输入消息内容');
                return;
            }

            const selectedRecipients = getSelectedRecipients();
            if (selectedRecipients.length === 0) {
                alert('请选择至少一个推送目标');
                return;
            }

            await sendCustomMessage(message, selectedRecipients);
        }









        // 发送自定义消息
        async function sendCustomMessage(message, recipientIDs) {
            if (!confirm('确定要发送测试消息吗？')) return;

            try {
                const response = await fetch('/api/admin/dingtalk/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        recipient_ids: recipientIDs
                    })
                });
                const result = await response.json();

                if (response.ok) {
                    alert('✅ 测试消息发送成功: ' + result.message);
                } else {
                    alert('❌ 测试消息发送失败: ' + result.error);
                }
            } catch (error) {
                alert('❌ 测试消息发送失败: ' + error.message);
            }
        }

        // 加载数据库中的任务组
        async function loadDatabaseTaskGroups() {
            try {
                const response = await fetch('/api/admin/database-task-groups');
                const taskGroups = await response.json();

                const select = document.getElementById('dbTaskGroup');
                if (!select) return;

                // 清空现有选项，保留"所有任务组"
                select.innerHTML = '<option value="">所有任务组</option>';

                // 添加数据库中实际存在的任务组
                taskGroups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group;
                    option.textContent = group;
                    select.appendChild(option);
                });

            } catch (error) {
                console.error('加载任务组失败:', error);
            }
        }

        // 加载配置文件中的任务组
        async function loadConfigTaskGroups() {
            try {
                const response = await fetch('/api/admin/task-groups');
                const taskGroups = await response.json();

                const container = document.getElementById('taskGroupCheckboxes');
                if (!container) return;

                // 清空现有内容
                container.innerHTML = '';

                // 添加配置文件中的任务组复选框
                if (Array.isArray(taskGroups)) {
                    taskGroups.forEach(group => {
                        const div = document.createElement('div');
                        div.style.marginBottom = '8px';

                        const checkboxContainer = document.createElement('span');
                        checkboxContainer.style.display = 'inline-block';
                        checkboxContainer.style.width = '20px';
                        checkboxContainer.style.verticalAlign = 'top';

                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.id = 'task_' + group.Name;
                        checkbox.value = group.Name;
                        checkbox.className = 'task-group-checkbox';

                        const label = document.createElement('label');
                        label.htmlFor = 'task_' + group.Name;
                        label.textContent = group.Name;
                        label.style.cursor = 'pointer';
                        label.style.display = 'inline-block';
                        label.style.verticalAlign = 'top';

                        checkboxContainer.appendChild(checkbox);
                        div.appendChild(checkboxContainer);
                        div.appendChild(label);
                        container.appendChild(div);
                    });
                }

            } catch (error) {
                console.error('加载配置任务组失败:', error);
            }
        }

        // 全选/取消全选任务组
        function selectAllTaskGroups(select) {
            const checkboxes = document.querySelectorAll('.task-group-checkbox');
            checkboxes.forEach(cb => cb.checked = select);
        }

        // 获取选中的任务组
        function getSelectedTaskGroups() {
            const checkboxes = document.querySelectorAll('.task-group-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 全选/取消全选数据库任务组
        function selectAllDbTaskGroups(select) {
            const checkboxes = document.querySelectorAll('.db-task-group-checkbox');
            checkboxes.forEach(cb => cb.checked = select);
        }

        // 获取选中的数据库任务组
        function getSelectedDbTaskGroups() {
            const checkboxes = document.querySelectorAll('.db-task-group-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 全选/取消全选接收者
        function selectAllRecipients(select) {
            const checkboxes = document.querySelectorAll('.recipient-checkbox:not(:disabled)');
            checkboxes.forEach(cb => cb.checked = select);
        }

        // 获取选中的接收者
        function getSelectedRecipients() {
            const checkboxes = document.querySelectorAll('.recipient-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value.split(':')[1]); // 去掉 'user:' 或 'group:' 前缀
        }

        // 加载数据库任务组选择框
        async function loadDatabaseTaskGroupsForSelection() {
            try {
                const response = await fetch('/api/admin/database-task-groups');
                const taskGroups = await response.json();

                const container = document.getElementById('dbTaskGroupCheckboxes');
                if (!container) return;

                // 清空现有内容
                container.innerHTML = '';

                if (taskGroups.length === 0) {
                    container.innerHTML = '<p>❌ 暂无任务组</p>';
                    return;
                }

                // 添加任务组复选框
                taskGroups.forEach(taskGroup => {
                    const div = document.createElement('div');
                    div.style.marginBottom = '6px';

                    const checkboxContainer = document.createElement('span');
                    checkboxContainer.style.display = 'inline-block';
                    checkboxContainer.style.width = '20px';
                    checkboxContainer.style.verticalAlign = 'top';

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.id = 'db_task_' + taskGroup;
                    checkbox.value = taskGroup;
                    checkbox.className = 'db-task-group-checkbox';

                    const label = document.createElement('label');
                    label.htmlFor = 'db_task_' + taskGroup;
                    label.textContent = taskGroup;
                    label.style.cursor = 'pointer';
                    label.style.display = 'inline-block';
                    label.style.verticalAlign = 'top';

                    checkboxContainer.appendChild(checkbox);
                    div.appendChild(checkboxContainer);
                    div.appendChild(label);
                    container.appendChild(div);
                });

            } catch (error) {
                console.error('加载数据库任务组选择框失败:', error);
                const container = document.getElementById('dbTaskGroupCheckboxes');
                if (container) {
                    container.innerHTML = '<p>❌ 加载任务组失败: ' + error.message + '</p>';
                }
            }
        }

        // 切换数据库记录显示/隐藏
        async function toggleDatabaseRecords() {
            const container = document.getElementById('databaseRecordsContainer');
            const button = document.getElementById('toggleDbRecordsBtn');

            if (!container || !button) return;

            if (container.style.display === 'none') {
                // 显示记录，加载数据
                container.style.display = 'block';
                button.textContent = '🔼 收回数据库记录';
                await loadDatabaseRecords();
            } else {
                // 隐藏记录
                container.style.display = 'none';
                button.textContent = '🔍 查看数据库记录';
            }
        }

        // 第二种：加载数据库记录（按时间点分组显示）
        async function loadDatabaseRecords() {
            const selectedTaskGroups = getSelectedDbTaskGroups();

            try {
                if (selectedTaskGroups.length === 0) {
                    const container = document.getElementById('databaseRecordsContainer');
                    if (container) {
                        container.innerHTML = '<p>⚠️ 请先选择任务组</p>';
                    }
                    return;
                }

                // 获取选中任务组的所有记录
                let allRecords = [];
                for (const taskGroup of selectedTaskGroups) {
                    const taskParams = new URLSearchParams();
                    taskParams.append('task_group', taskGroup);
                    const response = await fetch('/api/admin/history?' + taskParams.toString());
                    const records = await response.json();
                    allRecords = allRecords.concat(records);
                }

                // 按任务组和执行时间分组
                displayDatabaseRecordsByTime(allRecords);

            } catch (error) {
                const container = document.getElementById('databaseRecordsContainer');
                if (container) {
                    container.innerHTML = '<p>❌ 加载数据库记录失败: ' + error.message + '</p>';
                }
            }
        }

        // 按时间点分组显示数据库记录
        function displayDatabaseRecordsByTime(records) {
            const container = document.getElementById('databaseRecordsContainer');
            if (!container) return;

            if (records.length === 0) {
                container.innerHTML = '<p>❌ 没有找到数据库记录</p>';
                return;
            }

            // 按任务组和执行时间分组（使用分钟级别的时间分组）
            const groupedRecords = {};
            records.forEach(record => {
                const taskGroup = record.task_group;
                // 使用分钟级别的时间分组，忽略秒和毫秒
                const executedDate = new Date(record.executed_at);
                const executedTime = new Date(executedDate.getFullYear(), executedDate.getMonth(),
                    executedDate.getDate(), executedDate.getHours(), executedDate.getMinutes()).toLocaleString();

                if (!groupedRecords[taskGroup]) {
                    groupedRecords[taskGroup] = {};
                }

                if (!groupedRecords[taskGroup][executedTime]) {
                    groupedRecords[taskGroup][executedTime] = [];
                }

                groupedRecords[taskGroup][executedTime].push(record);
            });

            let html = '<h5>📋 按时间点选择数据 (共' + records.length + '条记录)</h5>';
            html += '<div style="margin-bottom: 15px;">';
            html += '<button onclick="selectAllTimeGroups(true)" class="btn btn-sm">全选所有时间点</button> ';
            html += '<button onclick="selectAllTimeGroups(false)" class="btn btn-sm">取消全选</button>';
            html += '</div>';

            // 显示每个任务组的时间点
            for (const taskGroup in groupedRecords) {
                html += '<div style="margin-bottom: 20px; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">';
                html += '<h6 style="margin-bottom: 10px; color: #333;">📊 ' + taskGroup + '</h6>';

                const timeGroups = groupedRecords[taskGroup];
                const sortedTimes = Object.keys(timeGroups).sort((a, b) => new Date(b) - new Date(a)); // 最新的在前

                sortedTimes.forEach(executedTime => {
                    const timeRecords = timeGroups[executedTime];
                    const recordIds = timeRecords.map(r => r.id);
                    const hasUnpushed = timeRecords.some(r => !r.push_status);

                    html += '<div style="margin-bottom: 8px; margin-left: 15px;">';
                    html += '<span style="display: inline-block; width: 20px; vertical-align: top;">';
                    html += '<input type="checkbox" class="time-group-checkbox" ';
                    html += 'data-record-ids="' + recordIds.join(',') + '" ';
                    html += 'data-task-group="' + taskGroup + '" ';
                    html += 'data-executed-time="' + executedTime + '">';
                    html += '</span>';
                    html += '<label style="cursor: pointer; display: inline-block; vertical-align: top;">';
                    html += '🕐 ' + executedTime + ' (' + timeRecords.length + '条记录)';
                    if (hasUnpushed) {
                        html += ' <span style="color: #28a745;">⏳ 有未推送</span>';
                    } else {
                        html += ' <span style="color: #6c757d;">✅ 全部已推送</span>';
                    }
                    html += '</label>';

                    // 添加详情按钮
                    const detailId = 'detail_' + taskGroup.replace(/[^a-zA-Z0-9]/g, '_') + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    html += ' <button onclick="toggleTimeGroupDetail(\'' + detailId + '\')" class="btn btn-sm" style="margin-left: 10px;">📋 详情</button>';
                    html += '</div>';

                    // 添加详情容器（默认隐藏）
                    html += '<div id="' + detailId + '" style="display: none; margin-left: 35px; margin-bottom: 10px; border: 1px solid #e0e0e0; padding: 10px; border-radius: 4px; background: #f8f9fa;">';
                    html += '<table class="table table-sm"><thead><tr><th>ID</th><th>查询词</th><th>地区</th><th>类型</th><th>推送状态</th></tr></thead><tbody>';

                    timeRecords.forEach(record => {
                        html += '<tr>';
                        html += '<td>' + record.id + '</td>';
                        html += '<td>' + record.query + '</td>';
                        html += '<td>' + record.geo + '</td>';
                        html += '<td>' + record.data_type + '</td>';
                        html += '<td>' + (record.push_status ? '✅ 已推送' : '⏳ 未推送') + '</td>';
                        html += '</tr>';
                    });

                    html += '</tbody></table></div>';
                });

                html += '</div>';
            }

            container.innerHTML = html;
        }

        // 全选/取消全选时间组
        function selectAllTimeGroups(select) {
            const checkboxes = document.querySelectorAll('.time-group-checkbox');
            checkboxes.forEach(cb => cb.checked = select);
        }

        // 获取选中的时间组对应的记录ID
        function getSelectedTimeGroupRecords() {
            const checkboxes = document.querySelectorAll('.time-group-checkbox:checked');
            let allRecordIds = [];

            checkboxes.forEach(cb => {
                const recordIds = cb.getAttribute('data-record-ids').split(',').map(id => parseInt(id));
                allRecordIds = allRecordIds.concat(recordIds);
            });

            return allRecordIds;
        }

        // 切换时间组详情显示
        function toggleTimeGroupDetail(detailId) {
            const detailContainer = document.getElementById(detailId);
            if (!detailContainer) return;

            if (detailContainer.style.display === 'none') {
                detailContainer.style.display = 'block';
            } else {
                detailContainer.style.display = 'none';
            }
        }

        // 推送选中的数据库记录给第一种选中的目标
        async function pushSelectedDbRecords() {
            // 检查是否选中了时间组
            const selectedRecords = getSelectedTimeGroupRecords();

            if (selectedRecords.length === 0) {
                alert('请先查看数据库记录并选择要推送的时间点');
                return;
            }

            const selectedRecipients = getSelectedRecipients();
            if (selectedRecipients.length === 0) {
                alert('请在第一种功能中选择推送目标');
                return;
            }

            await sendPushMessage(selectedRecords, selectedRecipients);
        }

        async function pushSelectedDbRecordsToAll() {
            // 检查是否选中了时间组
            const selectedRecords = getSelectedTimeGroupRecords();

            if (selectedRecords.length === 0) {
                alert('请先查看数据库记录并选择要推送的时间点');
                return;
            }

            await sendPushMessage(selectedRecords, []); // 空数组表示推送给所有人
        }







        // 执行任务并推送给第一种选中的目标
        async function pushTaskRecordsToJH() {
            const selectedTaskGroups = getSelectedTaskGroups();
            if (selectedTaskGroups.length === 0) {
                alert('请先选择至少一个任务组');
                return;
            }

            const selectedRecipients = getSelectedRecipients();
            if (selectedRecipients.length === 0) {
                alert('请在第一种功能中选择推送目标');
                return;
            }

            if (!confirm('确定要执行 ' + selectedTaskGroups.length + ' 个任务组并推送给选中目标吗？\n任务组：' + selectedTaskGroups.join(', '))) return;

            let successCount = 0;
            let failCount = 0;
            let allRecordIds = [];

            // 第一步：执行所有任务组
            for (const taskGroup of selectedTaskGroups) {
                try {
                    // 执行任务组
                    const executeResponse = await fetch('/api/admin/execute/' + taskGroup, {
                        method: 'POST'
                    });
                    const executeResult = await executeResponse.json();

                    if (executeResponse.ok) {
                        successCount++;
                    } else {
                        failCount++;
                        console.error('任务组 ' + taskGroup + ' 执行失败:', executeResult.error);
                    }
                } catch (error) {
                    failCount++;
                    console.error('任务组 ' + taskGroup + ' 执行失败:', error.message);
                }
            }

            // 第二步：如果有任务执行成功，收集所有最新数据并一次性推送
            if (successCount > 0) {
                try {
                    for (const taskGroup of selectedTaskGroups) {
                        try {
                            // 获取该任务组的最新记录
                            const historyResponse = await fetch('/api/admin/history?task_group=' + taskGroup);
                            const historyRecords = await historyResponse.json();

                            if (historyRecords.length > 0) {
                                // 获取最新时间的所有记录ID
                                const latestTime = new Date(historyRecords[0].executed_at);
                                const latestRecords = historyRecords.filter(record => {
                                    const recordTime = new Date(record.executed_at);
                                    return Math.abs(recordTime - latestTime) < 60000; // 1分钟内的记录
                                });

                                const recordIds = latestRecords.map(record => record.id);
                                allRecordIds = allRecordIds.concat(recordIds);
                            }
                        } catch (historyError) {
                            console.error('获取任务组 ' + taskGroup + ' 历史记录失败:', historyError.message);
                        }
                    }

                    // 一次性推送所有数据
                    if (allRecordIds.length > 0) {
                        await sendPushMessage(allRecordIds, selectedRecipients);
                    }
                } catch (pushError) {
                    console.error('推送失败:', pushError.message);
                }
            }

            alert('✅ 任务执行完成！\n成功：' + successCount + ' 个\n失败：' + failCount + ' 个');
        }

        // 执行任务并推送给所有人
        async function pushTaskRecordsToAll() {
            const selectedTaskGroups = getSelectedTaskGroups();
            if (selectedTaskGroups.length === 0) {
                alert('请先选择至少一个任务组');
                return;
            }

            if (!confirm('确定要执行 ' + selectedTaskGroups.length + ' 个任务组并推送给所有人吗？\n任务组：' + selectedTaskGroups.join(', '))) return;

            let successCount = 0;
            let failCount = 0;
            let allRecordIds = [];

            // 第一步：执行所有任务组
            for (const taskGroup of selectedTaskGroups) {
                try {
                    // 执行任务组
                    const executeResponse = await fetch('/api/admin/execute/' + taskGroup, {
                        method: 'POST'
                    });
                    const executeResult = await executeResponse.json();

                    if (executeResponse.ok) {
                        successCount++;
                    } else {
                        failCount++;
                        console.error('任务组 ' + taskGroup + ' 执行失败:', executeResult.error);
                    }
                } catch (error) {
                    failCount++;
                    console.error('任务组 ' + taskGroup + ' 执行失败:', error.message);
                }
            }

            // 第二步：如果有任务执行成功，收集所有最新数据并一次性推送
            if (successCount > 0) {
                try {
                    for (const taskGroup of selectedTaskGroups) {
                        try {
                            // 获取该任务组的最新记录
                            const historyResponse = await fetch('/api/admin/history?task_group=' + taskGroup);
                            const historyRecords = await historyResponse.json();

                            if (historyRecords.length > 0) {
                                // 获取最新时间的所有记录ID
                                const latestTime = new Date(historyRecords[0].executed_at);
                                const latestRecords = historyRecords.filter(record => {
                                    const recordTime = new Date(record.executed_at);
                                    return Math.abs(recordTime - latestTime) < 60000; // 1分钟内的记录
                                });

                                const recordIds = latestRecords.map(record => record.id);
                                allRecordIds = allRecordIds.concat(recordIds);
                            }
                        } catch (historyError) {
                            console.error('获取任务组 ' + taskGroup + ' 历史记录失败:', historyError.message);
                        }
                    }

                    // 一次性推送给所有人
                    if (allRecordIds.length > 0) {
                        await sendPushMessage(allRecordIds, []); // 空数组表示推送给所有人
                    }
                } catch (pushError) {
                    console.error('推送失败:', pushError.message);
                }
            }

            alert('✅ 任务执行完成！\n成功：' + successCount + ' 个\n失败：' + failCount + ' 个');
        }

        // 获取测试用户ID（从配置中读取）
        async function getTestUserID() {
            try {
                const response = await fetch('/api/admin/dingtalk/config');
                const config = await response.json();
                return config.default_test_user || '024326423122739246'; // fallback
            } catch (error) {
                console.warn('Failed to get test user ID from config, using fallback:', error);
                return '024326423122739246'; // fallback
            }
        }

        // 获取测试群组ID（从配置中读取）
        async function getTestGroupID() {
            try {
                const response = await fetch('/api/admin/dingtalk/config');
                const config = await response.json();
                return config.default_test_group || 'cidC1O/vEbs9hupD0d7yf2DWg=='; // fallback
            } catch (error) {
                console.warn('Failed to get test group ID from config, using fallback:', error);
                return 'cidC1O/vEbs9hupD0d7yf2DWg=='; // fallback
            }
        }



        // 发送推送消息（通用函数）
        async function sendPushMessage(recordIDs, recipientIDs, taskGroup) {
            if (!confirm('确定要发送推送消息吗？')) return;

            try {
                const requestBody = {
                    record_ids: recordIDs,
                    recipient_ids: recipientIDs
                };

                if (taskGroup) {
                    requestBody.task_group = taskGroup;
                }

                const response = await fetch('/api/admin/dingtalk/push', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });
                const result = await response.json();

                if (response.ok) {
                    alert('✅ 推送成功: ' + result.message);
                    // 重新加载相关数据
                    loadDatabaseRecords();
                } else {
                    alert('❌ 推送失败: ' + result.error);
                }
            } catch (error) {
                alert('❌ 推送失败: ' + error.message);
            }
        }

        // 关闭数据管理弹窗
        function closeDataManager() {
            const modals = document.querySelectorAll('div[style*="position: fixed"]');
            modals.forEach(modal => {
                if (modal.style.position === 'fixed') {
                    document.body.removeChild(modal);
                }
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTaskGroups();

            // 每30秒刷新一次日志
            setInterval(() => {
                document.getElementById('logContainer').innerHTML =
                    '<p>' + new Date().toLocaleString() + ' - 系统运行正常</p>' +
                    '<p>定时任务状态: ` + getStatusText(h.config.Scheduler.Enabled) + `</p>' +
                    '<p>下次执行时间: ` + h.config.Scheduler.Time + `</p>';
            }, 30000);
        });
    </script>
</body>
</html>`

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// GetTaskGroups returns task groups configuration (for main page)
func (h *AdminHandler) GetTaskGroups(c *gin.Context) {
	c.JSON(http.StatusOK, h.config.TaskGroups)
}

// GetDatabaseTaskGroups returns task groups from database (for DingTalk push interface)
func (h *AdminHandler) GetDatabaseTaskGroups(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusOK, []string{})
		return
	}

	taskGroups, err := h.database.GetDistinctTaskGroups()
	if err != nil {
		c.JSON(http.StatusOK, []string{})
		return
	}

	c.JSON(http.StatusOK, taskGroups)
}

// GetDataStats returns database statistics
func (h *AdminHandler) GetDataStats(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Database not available"})
		return
	}

	stats, err := h.database.GetDataStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Add data maintenance info
	if h.dataMaintenanceService != nil {
		nextCleanup := h.dataMaintenanceService.GetNextCleanupTime()
		if nextCleanup != nil {
			stats["next_cleanup"] = nextCleanup.Format("2006-01-02 15:04:05 MST")
		}
		stats["retention_days"] = h.config.DataMaintenance.RetentionDays
		stats["cleanup_enabled"] = h.config.DataMaintenance.Enabled
	}

	c.JSON(http.StatusOK, stats)
}

// ManualCleanup performs manual data cleanup
func (h *AdminHandler) ManualCleanup(c *gin.Context) {
	if h.dataMaintenanceService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Data maintenance service not available"})
		return
	}

	deletedCount, err := h.dataMaintenanceService.ManualCleanup()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Manual cleanup completed successfully",
		"deleted_count":  deletedCount,
		"retention_days": h.config.DataMaintenance.RetentionDays,
	})
}

// ExecuteTaskGroup executes a specific task group
func (h *AdminHandler) ExecuteTaskGroup(c *gin.Context) {
	taskGroupName := c.Param("name")

	if h.scheduler != nil {
		err := h.scheduler.ExecuteTaskGroupManually(taskGroupName)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Task group executed successfully"})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Scheduler service not available"})
	}
}

// GetResults returns scheduler results
func (h *AdminHandler) GetResults(c *gin.Context) {
	if h.scheduler != nil {
		results := h.scheduler.GetLatestResults()
		c.JSON(http.StatusOK, results)
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Scheduler service not available"})
	}
}

// GetDatabaseStats returns database statistics
func (h *AdminHandler) GetDatabaseStats(c *gin.Context) {
	if h.database != nil {
		stats, err := h.database.GetExecutionStats()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, stats)
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Database service not available"})
	}
}

// GetTrendsHistory returns trends history with filtering support
func (h *AdminHandler) GetTrendsHistory(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Database service not available"})
		return
	}

	// Parse query parameters
	query := c.Query("query")
	taskGroup := c.Query("task_group")
	dataType := c.Query("data_type")
	geo := c.Query("geo")

	days := 30 // Default to 30 days
	if daysParam := c.Query("days"); daysParam != "" {
		if d, err := strconv.Atoi(daysParam); err == nil && d > 0 {
			days = d
		}
	}

	// If query parameter is provided, use the old behavior for backward compatibility
	if query != "" {
		records, err := h.database.GetTrendsDataByQuery(query, days)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, records)
		return
	}

	// Use new filtering method
	records, err := h.database.GetTrendsDataWithFilters(taskGroup, dataType, geo, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, records)
}

// GetWorkdayInfo returns workday configuration information
func (h *AdminHandler) GetWorkdayInfo(c *gin.Context) {
	if h.scheduler == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Scheduler not available"})
		return
	}

	info := h.scheduler.GetWorkdayInfo()
	c.JSON(http.StatusOK, info)
}

// GetRecordDetails returns detailed information for a specific record and its children
func (h *AdminHandler) GetRecordDetails(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid record ID"})
		return
	}

	// Get main record and all its child records
	mainRecord, childRecords, err := h.database.GetBatchRecords(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return both main record and child records
	response := gin.H{
		"main_record":    mainRecord,
		"child_records":  childRecords,
		"total_children": len(childRecords),
	}

	c.JSON(http.StatusOK, response)
}

// DeleteTrendsRecord deletes a specific trends record
func (h *AdminHandler) DeleteTrendsRecord(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid record ID"})
		return
	}

	err = h.database.DeleteTrendsRecord(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Record deleted successfully"})
}

// DeleteTrendsRecords deletes multiple trends records
func (h *AdminHandler) DeleteTrendsRecords(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	var request struct {
		IDs []int `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	deleted, err := h.database.DeleteTrendsRecords(request.IDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Records deleted successfully",
		"deleted": deleted,
	})
}

// ExportTrendsData exports trends data in specified format
func (h *AdminHandler) ExportTrendsData(c *gin.Context) {
	if h.database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
		return
	}

	format := c.Query("format")
	if format == "" {
		format = "csv"
	}

	taskGroup := c.Query("task_group")
	dataType := c.Query("data_type")
	geo := c.Query("geo")
	days := 30
	if d := c.Query("days"); d != "" {
		if parsed, err := strconv.Atoi(d); err == nil && parsed > 0 {
			days = parsed
		}
	}

	data, err := h.database.ExportTrendsData(taskGroup, dataType, geo, days, format)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	filename := fmt.Sprintf("trends_data_%s.%s", time.Now().Format("20060102_150405"), format)

	switch format {
	case "csv":
		c.Header("Content-Type", "text/csv")
	case "json":
		c.Header("Content-Type", "application/json")
	default:
		c.Header("Content-Type", "application/octet-stream")
	}

	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Data(http.StatusOK, c.GetHeader("Content-Type"), data)
}

// Helper functions for template
func getStatusClass(enabled bool) string {
	if enabled {
		return "enabled"
	}
	return "disabled"
}

func getStatusText(enabled bool) string {
	if enabled {
		return "启用"
	}
	return "禁用"
}

func getKeepAliveInfo(service *services.WindowsKeepAliveService) string {
	if service == nil {
		return ""
	}

	status := service.GetStatus()
	if running, ok := status["running"].(bool); ok && running {
		if interval, ok := status["heartbeat_interval"].(string); ok {
			return fmt.Sprintf("(心跳: %s)", interval)
		}
		return "(运行中)"
	}
	return "(已停止)"
}

func getWorkdayModeText(workdaysOnly bool, pattern string) string {
	if !workdaysOnly {
		return "每天执行"
	}

	switch pattern {
	case "MON-FRI":
		return "工作日 (周一到周五)"
	case "MON-SAT":
		return "工作日 (周一到周六)"
	case "MON-THU":
		return "工作日 (周一到周四)"
	case "TUE-FRI":
		return "工作日 (周二到周五)"
	case "DAILY":
		return "每天执行"
	default:
		return fmt.Sprintf("自定义 (%s)", pattern)
	}
}
