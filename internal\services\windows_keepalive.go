package services

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"runtime"
	"syscall"
	"time"

	"go_lilybot_api_trends/internal/config"
)

// Windows API constants
const (
	ES_CONTINUOUS        = 0x80000000
	ES_SYSTEM_REQUIRED   = 0x00000001
	ES_AWAYMODE_REQUIRED = 0x00000040

	HIGH_PRIORITY_CLASS     = 0x00000080
	REALTIME_PRIORITY_CLASS = 0x00000100
)

// WindowsKeepAliveService handles Windows-specific keep-alive functionality
type WindowsKeepAliveService struct {
	config     *config.KeepAliveConfig
	httpClient *http.Client
	ctx        context.Context
	cancel     context.CancelFunc

	// Windows API functions
	kernel32                *syscall.LazyDLL
	setThreadExecutionState *syscall.LazyProc
	setPriorityClass        *syscall.LazyProc
	getCurrentProcess       *syscall.LazyProc
}

// NewWindowsKeepAliveService creates a new Windows keep-alive service
func NewWindowsKeepAliveService(cfg *config.KeepAliveConfig) *WindowsKeepAliveService {
	ctx, cancel := context.WithCancel(context.Background())

	service := &WindowsKeepAliveService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		ctx:    ctx,
		cancel: cancel,
	}

	// Initialize Windows API functions only on Windows
	if runtime.GOOS == "windows" {
		service.initWindowsAPI()
	}

	return service
}

// initWindowsAPI initializes Windows API function pointers
func (w *WindowsKeepAliveService) initWindowsAPI() {
	w.kernel32 = syscall.NewLazyDLL("kernel32.dll")
	w.setThreadExecutionState = w.kernel32.NewProc("SetThreadExecutionState")
	w.setPriorityClass = w.kernel32.NewProc("SetPriorityClass")
	w.getCurrentProcess = w.kernel32.NewProc("GetCurrentProcess")
}

// Start starts the keep-alive service
func (w *WindowsKeepAliveService) Start() error {
	if !w.config.Enabled {
		log.Println("🔋 Windows Keep-Alive service is disabled")
		return nil
	}

	if runtime.GOOS != "windows" {
		log.Println("🔋 Windows Keep-Alive service is only available on Windows")
		return nil
	}

	log.Println("🔋 Starting Windows Keep-Alive service...")

	// Set initial system state
	if err := w.preventSystemSleep(); err != nil {
		log.Printf("⚠️  Failed to prevent system sleep: %v", err)
	}

	// Set process priority
	if err := w.setHighPriority(); err != nil {
		log.Printf("⚠️  Failed to set high priority: %v", err)
	}

	// Start keep-alive goroutine
	go w.keepAliveLoop()

	log.Printf("✅ Windows Keep-Alive service started (interval: %v)", w.config.HeartbeatInterval)
	return nil
}

// Stop stops the keep-alive service
func (w *WindowsKeepAliveService) Stop() {
	if w.cancel != nil {
		w.cancel()
	}

	// Restore normal execution state
	if runtime.GOOS == "windows" && w.setThreadExecutionState != nil {
		w.setThreadExecutionState.Call(uintptr(ES_CONTINUOUS))
		log.Println("🔋 Windows Keep-Alive service stopped, execution state restored")
	}
}

// preventSystemSleep prevents the system from going to sleep
func (w *WindowsKeepAliveService) preventSystemSleep() error {
	if w.setThreadExecutionState == nil {
		return fmt.Errorf("SetThreadExecutionState API not available")
	}

	// Set execution state to prevent system sleep and display sleep
	state := ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_AWAYMODE_REQUIRED
	ret, _, err := w.setThreadExecutionState.Call(uintptr(state))

	if ret == 0 {
		return fmt.Errorf("SetThreadExecutionState failed: %v", err)
	}

	log.Println("🔋 System sleep prevention enabled")
	return nil
}

// setHighPriority sets the process to high priority
func (w *WindowsKeepAliveService) setHighPriority() error {
	if w.setPriorityClass == nil || w.getCurrentProcess == nil {
		return fmt.Errorf("Priority APIs not available")
	}

	// Get current process handle
	processHandle, _, _ := w.getCurrentProcess.Call()

	// Set priority class based on configuration
	var priorityClass uintptr
	switch w.config.ProcessPriority {
	case "high":
		priorityClass = HIGH_PRIORITY_CLASS
	case "realtime":
		priorityClass = REALTIME_PRIORITY_CLASS
		log.Println("⚠️  Using REALTIME priority - use with caution!")
	default:
		priorityClass = HIGH_PRIORITY_CLASS
	}

	ret, _, err := w.setPriorityClass.Call(processHandle, priorityClass)
	if ret == 0 {
		return fmt.Errorf("SetPriorityClass failed: %v", err)
	}

	log.Printf("🔋 Process priority set to %s", w.config.ProcessPriority)
	return nil
}

// keepAliveLoop runs the main keep-alive loop
func (w *WindowsKeepAliveService) keepAliveLoop() {
	ticker := time.NewTicker(w.config.HeartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-w.ctx.Done():
			log.Println("🔋 Keep-alive loop stopped")
			return
		case <-ticker.C:
			w.performKeepAlive()
		}
	}
}

// performKeepAlive performs keep-alive operations
func (w *WindowsKeepAliveService) performKeepAlive() {
	log.Println("🔋 Performing keep-alive operations...")

	// 1. Refresh system sleep prevention
	if err := w.preventSystemSleep(); err != nil {
		log.Printf("⚠️  Failed to refresh sleep prevention: %v", err)
	}

	// 2. Perform network activity if enabled
	if w.config.NetworkHeartbeat {
		w.performNetworkHeartbeat()
	}

	// 3. Log system status
	w.logSystemStatus()

	log.Println("✅ Keep-alive operations completed")
}

// performNetworkHeartbeat performs a network request to keep connections alive
func (w *WindowsKeepAliveService) performNetworkHeartbeat() {
	if w.config.HeartbeatURL == "" {
		// Use a simple HTTP request to a reliable service
		w.config.HeartbeatURL = "https://www.google.com/generate_204"
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", w.config.HeartbeatURL, nil)
	if err != nil {
		log.Printf("⚠️  Failed to create heartbeat request: %v", err)
		return
	}

	resp, err := w.httpClient.Do(req)
	if err != nil {
		log.Printf("⚠️  Network heartbeat failed: %v", err)
		return
	}
	defer resp.Body.Close()

	log.Printf("🌐 Network heartbeat successful (status: %d)", resp.StatusCode)
}

// logSystemStatus logs current system status
func (w *WindowsKeepAliveService) logSystemStatus() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	log.Printf("📊 System Status - Goroutines: %d, Memory: %.2f MB, GC Cycles: %d",
		runtime.NumGoroutine(),
		float64(m.Alloc)/1024/1024,
		m.NumGC)
}

// IsRunning returns whether the keep-alive service is running
func (w *WindowsKeepAliveService) IsRunning() bool {
	select {
	case <-w.ctx.Done():
		return false
	default:
		return true
	}
}

// GetStatus returns the current status of the keep-alive service
func (w *WindowsKeepAliveService) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"enabled":            w.config.Enabled,
		"running":            w.IsRunning(),
		"heartbeat_interval": w.config.HeartbeatInterval.String(),
		"process_priority":   w.config.ProcessPriority,
		"network_heartbeat":  w.config.NetworkHeartbeat,
		"platform":           runtime.GOOS,
	}
}
