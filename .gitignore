# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
bin/
build/
dist/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.cover
*.coverprofile

# Air (hot reload) temporary files
tmp/

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.json
config.yaml
config.yml

# Backup files
*.bak
*.backup

# Archive files
*.tar
*.tar.gz
*.zip
*.rar

# Documentation build
docs/_build/

# Local development
.local/
local/
