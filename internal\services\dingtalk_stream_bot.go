package services

import (
	"context"
	"fmt"
	"log"
	"strings"

	"go_lilybot_api_trends/internal/config"

	"github.com/open-dingtalk/dingtalk-stream-sdk-go/chatbot"
	"github.com/open-dingtalk/dingtalk-stream-sdk-go/client"
	"github.com/open-dingtalk/dingtalk-stream-sdk-go/logger"
)

// DingTalkStreamBot handles the real Stream mode bot functionality
type DingTalkStreamBot struct {
	config          *config.DingTalkConfig
	dingTalkService *DingTalkService
	streamClient    *client.StreamClient
}

// NewDingTalkStreamBot creates a new Stream bot instance
func NewDingTalkStreamBot(config *config.DingTalkConfig, dingTalkService *DingTalkService) *DingTalkStreamBot {
	return &DingTalkStreamBot{
		config:          config,
		dingTalkService: dingTalkService,
	}
}

// OnChatBotMessageReceived handles incoming messages from users
func (bot *DingTalkStreamBot) OnChatBotMessageReceived(ctx context.Context, data *chatbot.BotCallbackDataModel) ([]byte, error) {
	log.Printf("📱 ========== 收到Stream消息 ==========")
	log.Printf("📱 发送者ID: %s", data.SenderId)
	log.Printf("📱 发送者昵称: %s", data.SenderNick)
	log.Printf("📱 对话ID: %s", data.ConversationId)
	log.Printf("📱 对话类型: %s", data.ConversationType)
	log.Printf("📱 消息内容: %s", strings.TrimSpace(data.Text.Content))
	log.Printf("📱 是否在@列表: %t", data.IsInAtList)

	// 检查是否有其他ID字段
	log.Printf("📱 调试 - 检查更多字段:")
	log.Printf("📱 - SenderStaffId: %s", data.SenderStaffId)
	log.Printf("📱 - SenderCorpId: %s", data.SenderCorpId)
	log.Printf("📱 - ChatbotCorpId: %s", data.ChatbotCorpId)
	log.Printf("📱 - ChatbotUserId: %s", data.ChatbotUserId)
	log.Printf("📱 - MsgId: %s", data.MsgId)
	log.Printf("📱 - CreateAt: %d", data.CreateAt)
	log.Printf("📱 - Msgtype: %s", data.Msgtype)
	log.Printf("📱 - ConversationTitle: %s", data.ConversationTitle)

	// 打印完整数据结构
	log.Printf("📱 完整数据结构: %+v", data)

	// 只收集用户/群组ID，不发送自动回复（Stream模式直接回复）
	err := bot.collectSenderInfo(data.SenderId, data.SenderNick, data.ConversationId, data.ConversationType, data.SenderStaffId)
	if err != nil {
		log.Printf("❌ 收集用户信息失败: %v", err)
		// 即使收集失败，也要发送回复
	}

	// 构建自动回复消息
	replyText := fmt.Sprintf("🤖 %s\n\n你好 %s！\n\n%s\n\n✅ 你的ID已被记录，可以接收推送消息了！\n\n📊 我会定期推送Google Trends数据给你。",
		bot.config.BotName, data.SenderNick, bot.config.BotDescription)

	// 使用Stream SDK发送回复
	replier := chatbot.NewChatbotReplier()
	if err := replier.SimpleReplyText(ctx, data.SessionWebhook, []byte(replyText)); err != nil {
		log.Printf("❌ Stream回复失败: %v", err)
		return nil, err
	}

	log.Printf("✅ Stream自动回复发送成功给 %s", data.SenderNick)
	log.Printf("📱 ========== Stream消息处理完成 ==========")

	return []byte(""), nil
}

// Start starts the Stream bot service
func (bot *DingTalkStreamBot) Start(ctx context.Context) error {
	if !bot.config.BotEnabled {
		log.Println("📱 DingTalk bot is disabled, skipping Stream bot")
		return nil
	}

	log.Println("📱 ========== 启动钉钉Stream机器人 ==========")
	log.Printf("📱 ClientID: %s", bot.config.ClientID)
	log.Printf("📱 机器人名称: %s", bot.config.BotName)

	// 设置Stream SDK日志
	logger.SetLogger(logger.NewStdTestLogger())

	// 创建Stream客户端
	bot.streamClient = client.NewStreamClient(
		client.WithAppCredential(
			client.NewAppCredentialConfig(bot.config.ClientID, bot.config.ClientSecret),
		),
	)

	// 注册机器人消息回调
	bot.streamClient.RegisterChatBotCallbackRouter(bot.OnChatBotMessageReceived)

	log.Println("📱 正在连接到钉钉Stream服务器...")

	// 启动Stream客户端
	err := bot.streamClient.Start(ctx)
	if err != nil {
		log.Printf("❌ Stream客户端启动失败: %v", err)
		return fmt.Errorf("failed to start stream client: %w", err)
	}

	log.Println("✅ 钉钉Stream机器人启动成功！")
	return nil
}

// Stop stops the Stream bot service
func (bot *DingTalkStreamBot) Stop() {
	if bot.streamClient != nil {
		log.Println("📱 正在停止Stream机器人...")
		bot.streamClient.Close()
		log.Println("✅ Stream机器人已停止")
	}
}

// IsRunning checks if the Stream bot is running
func (bot *DingTalkStreamBot) IsRunning() bool {
	return bot.streamClient != nil
}

// collectSenderInfo collects sender information without sending auto-reply
func (bot *DingTalkStreamBot) collectSenderInfo(senderID, senderName, conversationID, conversationType, senderStaffID string) error {
	log.Printf("📱 开始收集Stream消息发送者信息...")
	log.Printf("📱 发送者ID: %s, 发送者名称: %s", senderID, senderName)
	log.Printf("📱 对话ID: %s, 对话类型: %s", conversationID, conversationType)
	log.Printf("📱 真实StaffID: %s", senderStaffID)

	// 确定是用户还是群组
	var idType, targetID, targetName string

	if conversationType == "1" { // 单聊
		idType = "user"
		// 使用真实的SenderStaffId而不是加密ID
		targetID = senderStaffID
		targetName = senderName
		log.Printf("📱 识别为单聊消息，真实用户ID: %s, 用户名: %s", targetID, targetName)
	} else { // 群聊
		idType = "group"
		targetID = conversationID
		targetName = senderName + "的群组" // 可以后续优化获取真实群名
		log.Printf("📱 识别为群聊消息，群组ID: %s, 群组名: %s, 发送者: %s", targetID, targetName, senderName)
	}

	// 保存到ID配置文件
	log.Printf("📱 保存%s信息到配置文件: %s -> %s", idType, targetID, targetName)
	return bot.dingTalkService.SaveToIDConfigFile(idType, targetID, targetName)
}
