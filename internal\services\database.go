package services

import (
	"bytes"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/models"

	_ "modernc.org/sqlite"
)

// DatabaseService handles SQLite database operations
type DatabaseService struct {
	db *sql.DB
}

// TrendsRecord represents a trends data record in the database
type TrendsRecord struct {
	ID           int                    `json:"id"`
	TaskGroup    string                 `json:"task_group"`
	Query        string                 `json:"query"`
	Geo          string                 `json:"geo"`
	DateRange    string                 `json:"date_range"`
	DataType     string                 `json:"data_type"`
	ExecutedAt   time.Time              `json:"executed_at"`
	AvgInterest  int                    `json:"avg_interest"`
	DataPoints   int                    `json:"data_points"`
	PeakDate     string                 `json:"peak_date,omitempty"`
	BatchID      string                 `json:"batch_id,omitempty"`       // 批次ID，用于关联同一次任务执行的多条记录
	ParentTaskID string                 `json:"parent_task_id,omitempty"` // 父任务ID，用于标识主任务
	RawData      *models.TrendsResponse `json:"raw_data"`
}

// NewDatabaseService creates a new database service
func NewDatabaseService(dbPath string) (*DatabaseService, error) {
	// Ensure data directory exists
	if err := os.MkdirAll(filepath.Dir(dbPath), 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// Open database connection
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	service := &DatabaseService{db: db}

	// Initialize database schema
	if err := service.initSchema(); err != nil {
		return nil, fmt.Errorf("failed to initialize database schema: %w", err)
	}

	// Run database migrations
	if err := service.runMigrations(); err != nil {
		return nil, fmt.Errorf("failed to run database migrations: %w", err)
	}

	log.Printf("📊 Database initialized: %s", dbPath)
	return service, nil
}

// initSchema creates the database tables
func (d *DatabaseService) initSchema() error {
	schema := `
	CREATE TABLE IF NOT EXISTS trends_data (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_group TEXT NOT NULL,
		query TEXT NOT NULL,
		geo TEXT,
		date_range TEXT NOT NULL,
		data_type TEXT NOT NULL,
		executed_at DATETIME NOT NULL,
		avg_interest INTEGER DEFAULT 0,
		data_points INTEGER DEFAULT 0,
		peak_date TEXT,
		raw_data TEXT NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_trends_task_group ON trends_data(task_group);
	CREATE INDEX IF NOT EXISTS idx_trends_query ON trends_data(query);
	CREATE INDEX IF NOT EXISTS idx_trends_executed_at ON trends_data(executed_at);

	-- Add DingTalk push status columns if they don't exist (ignore errors if columns already exist)
	-- ALTER TABLE trends_data ADD COLUMN push_status INTEGER DEFAULT 0;
	-- ALTER TABLE trends_data ADD COLUMN pushed_at DATETIME;

	-- DingTalk recipients table removed - now using configuration file
	CREATE INDEX IF NOT EXISTS idx_trends_geo ON trends_data(geo);

	CREATE TABLE IF NOT EXISTS execution_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_group TEXT NOT NULL,
		status TEXT NOT NULL,
		message TEXT,
		executed_at DATETIME NOT NULL,
		duration_ms INTEGER,
		queries_processed INTEGER DEFAULT 0,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_logs_executed_at ON execution_logs(executed_at);
	CREATE INDEX IF NOT EXISTS idx_logs_task_group ON execution_logs(task_group);
	`

	_, err := d.db.Exec(schema)
	return err
}

// runMigrations runs database migrations to update schema
func (d *DatabaseService) runMigrations() error {
	// Check if batch_id and parent_task_id columns exist
	var columnExists int
	err := d.db.QueryRow("SELECT COUNT(*) FROM pragma_table_info('trends_data') WHERE name IN ('batch_id', 'parent_task_id')").Scan(&columnExists)
	if err != nil {
		return fmt.Errorf("failed to check column existence: %w", err)
	}

	// If columns don't exist, add them
	if columnExists < 2 {
		log.Printf("📊 Running database migration: adding batch_id and parent_task_id columns")

		migrations := []string{
			"ALTER TABLE trends_data ADD COLUMN batch_id TEXT",
			"ALTER TABLE trends_data ADD COLUMN parent_task_id TEXT",
		}

		for _, migration := range migrations {
			_, err := d.db.Exec(migration)
			if err != nil {
				// Ignore "duplicate column name" errors
				if !strings.Contains(err.Error(), "duplicate column name") {
					return fmt.Errorf("failed to run migration '%s': %w", migration, err)
				}
			}
		}

		log.Printf("✅ Database migration completed successfully")
	}

	return nil
}

// SaveTrendsData saves trends data to database
func (d *DatabaseService) SaveTrendsData(taskGroup, query, geo, dateRange, dataType string, data *models.TrendsResponse) error {
	return d.SaveTrendsDataWithBatch(taskGroup, query, geo, dateRange, dataType, data, "", "")
}

// SaveTrendsDataWithBatch saves trends data to database with batch and parent task IDs
func (d *DatabaseService) SaveTrendsDataWithBatch(taskGroup, query, geo, dateRange, dataType string, data *models.TrendsResponse, batchID, parentTaskID string) error {
	// Extract key metrics based on data type
	avgInterest := 0
	dataPoints := 0
	peakDate := ""

	switch dataType {
	case "EXPLORE":
		// For EXPLORE mode, extract metrics from RelatedTopics
		if data.RelatedTopics != nil {
			dataPoints = len(data.RelatedTopics.Top) + len(data.RelatedTopics.Rising)

			// Calculate average interest from top topics
			if len(data.RelatedTopics.Top) > 0 {
				totalValue := 0
				validCount := 0
				for _, topic := range data.RelatedTopics.Top {
					if topic.ExtractedValue > 0 {
						totalValue += topic.ExtractedValue
						validCount++
					}
				}
				if validCount > 0 {
					avgInterest = totalValue / validCount
				}
			}

			// Set peak date to current date for EXPLORE mode
			peakDate = time.Now().Format("2006-01-02")
		}

	case "RELATED_QUERIES":
		// For RELATED_QUERIES mode, extract metrics from RelatedQueries
		if data.RelatedQueries != nil {
			dataPoints = len(data.RelatedQueries.Top) + len(data.RelatedQueries.Rising)

			// Calculate average interest from top queries
			if len(data.RelatedQueries.Top) > 0 {
				totalValue := 0
				validCount := 0
				for _, query := range data.RelatedQueries.Top {
					if query.ExtractedValue > 0 {
						totalValue += query.ExtractedValue
						validCount++
					}
				}
				if validCount > 0 {
					avgInterest = totalValue / validCount
				}
			}

			// Set peak date to current date for RELATED_QUERIES mode
			peakDate = time.Now().Format("2006-01-02")
		}

	case "RELATED_TOPICS":
		// For RELATED_TOPICS mode, extract metrics from RelatedTopics
		if data.RelatedTopics != nil {
			dataPoints = len(data.RelatedTopics.Top) + len(data.RelatedTopics.Rising)

			// Calculate average interest from top topics
			if len(data.RelatedTopics.Top) > 0 {
				totalValue := 0
				validCount := 0
				for _, topic := range data.RelatedTopics.Top {
					if topic.ExtractedValue > 0 {
						totalValue += topic.ExtractedValue
						validCount++
					}
				}
				if validCount > 0 {
					avgInterest = totalValue / validCount
				}
			}

			// Set peak date to current date for RELATED_TOPICS mode
			peakDate = time.Now().Format("2006-01-02")
		}

	case "TRENDING_NOW":
		// For TRENDING_NOW, we might not have InterestOverTime either
		// Use the trending search data directly
		dataPoints = 1   // Each trending search is one data point
		avgInterest = 50 // Default interest level for trending searches
		peakDate = time.Now().Format("2006-01-02")

	default:
		// For TIMESERIES and other modes, use InterestOverTime
		if data.InterestOverTime != nil {
			dataPoints = len(data.InterestOverTime.TimelineData)
			if len(data.InterestOverTime.Averages) > 0 {
				avgInterest = data.InterestOverTime.Averages[0].Value
			}

			// Find peak date
			maxValue := 0
			for _, timeline := range data.InterestOverTime.TimelineData {
				if len(timeline.Values) > 0 && timeline.Values[0].ExtractedValue > maxValue {
					maxValue = timeline.Values[0].ExtractedValue
					peakDate = timeline.Date
				}
			}
		}
	}

	// Serialize raw data
	rawDataJSON, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to serialize raw data: %w", err)
	}

	// Insert into database
	query_sql := `
		INSERT INTO trends_data (
			task_group, query, geo, date_range, data_type,
			executed_at, avg_interest, data_points, peak_date, batch_id, parent_task_id, raw_data
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err = d.db.Exec(query_sql, taskGroup, query, geo, dateRange, dataType,
		time.Now(), avgInterest, dataPoints, peakDate, batchID, parentTaskID, string(rawDataJSON))

	if err != nil {
		return fmt.Errorf("failed to save trends data: %w", err)
	}

	log.Printf("💾 Saved trends data: %s - %s (avg: %d, points: %d)", taskGroup, query, avgInterest, dataPoints)
	return nil
}

// LogExecution logs task execution
func (d *DatabaseService) LogExecution(taskGroup, status, message string, duration time.Duration, queriesProcessed int) error {
	query := `
		INSERT INTO execution_logs (task_group, status, message, executed_at, duration_ms, queries_processed)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := d.db.Exec(query, taskGroup, status, message, time.Now(), duration.Milliseconds(), queriesProcessed)
	if err != nil {
		return fmt.Errorf("failed to log execution: %w", err)
	}

	return nil
}

// GetLatestTrendsData returns the latest trends data for each query
func (d *DatabaseService) GetLatestTrendsData(limit int) ([]TrendsRecord, error) {
	// Apply same time filter as data management to ensure consistency
	query := `
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date, batch_id, parent_task_id, raw_data
		FROM trends_data
		WHERE executed_at >= datetime('now', '-30 days')
		ORDER BY executed_at DESC
		LIMIT ?
	`

	rows, err := d.db.Query(query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query latest trends data: %w", err)
	}
	defer rows.Close()

	var records []TrendsRecord
	for rows.Next() {
		var record TrendsRecord
		var rawDataJSON string
		var batchID, parentTaskID sql.NullString

		err := rows.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
			&record.DateRange, &record.DataType, &record.ExecutedAt,
			&record.AvgInterest, &record.DataPoints, &record.PeakDate, &batchID, &parentTaskID, &rawDataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trends record: %w", err)
		}

		// Handle NULL values
		if batchID.Valid {
			record.BatchID = batchID.String
		}
		if parentTaskID.Valid {
			record.ParentTaskID = parentTaskID.String
		}

		// Deserialize raw data
		var rawData models.TrendsResponse
		if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
			log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
		} else {
			record.RawData = &rawData
		}

		records = append(records, record)
	}

	return records, nil
}

// GetTrendsDataByQuery returns trends data for a specific query
func (d *DatabaseService) GetTrendsDataByQuery(query string, days int) ([]TrendsRecord, error) {
	sql_query := `
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date, batch_id, parent_task_id, raw_data
		FROM trends_data
		WHERE query = ? AND executed_at >= datetime('now', '-' || ? || ' days')
		ORDER BY executed_at DESC
	`

	rows, err := d.db.Query(sql_query, query, days)
	if err != nil {
		return nil, fmt.Errorf("failed to query trends data by query: %w", err)
	}
	defer rows.Close()

	var records []TrendsRecord
	for rows.Next() {
		var record TrendsRecord
		var rawDataJSON string
		var batchID, parentTaskID sql.NullString

		err := rows.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
			&record.DateRange, &record.DataType, &record.ExecutedAt,
			&record.AvgInterest, &record.DataPoints, &record.PeakDate, &batchID, &parentTaskID, &rawDataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trends record: %w", err)
		}

		// Handle NULL values
		if batchID.Valid {
			record.BatchID = batchID.String
		}
		if parentTaskID.Valid {
			record.ParentTaskID = parentTaskID.String
		}

		// Deserialize raw data
		var rawData models.TrendsResponse
		if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
			log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
		} else {
			record.RawData = &rawData
		}

		records = append(records, record)
	}

	return records, nil
}

// GetTrendsDataWithFilters returns trends data with multiple filter options
func (d *DatabaseService) GetTrendsDataWithFilters(taskGroup, dataType, geo string, days int) ([]TrendsRecord, error) {
	// Build query with filters
	query := `
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date, batch_id, parent_task_id, raw_data
		FROM trends_data
		WHERE executed_at >= datetime('now', '-' || ? || ' days')
	`
	args := []interface{}{days}

	if taskGroup != "" {
		query += " AND task_group = ?"
		args = append(args, taskGroup)
	}
	if dataType != "" {
		query += " AND data_type = ?"
		args = append(args, dataType)
	}
	if geo != "" {
		query += " AND geo = ?"
		args = append(args, geo)
	}

	query += " ORDER BY executed_at DESC"

	rows, err := d.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query trends data with filters: %w", err)
	}
	defer rows.Close()

	var records []TrendsRecord
	for rows.Next() {
		var record TrendsRecord
		var rawDataJSON string
		var batchID, parentTaskID sql.NullString

		err := rows.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
			&record.DateRange, &record.DataType, &record.ExecutedAt,
			&record.AvgInterest, &record.DataPoints, &record.PeakDate, &batchID, &parentTaskID, &rawDataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trends record: %w", err)
		}

		// Handle NULL values
		if batchID.Valid {
			record.BatchID = batchID.String
		}
		if parentTaskID.Valid {
			record.ParentTaskID = parentTaskID.String
		}

		// Deserialize raw data
		var rawData models.TrendsResponse
		if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
			log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
		} else {
			record.RawData = &rawData
		}

		records = append(records, record)
	}

	return records, nil
}

// GetBatchRecords returns a main record and all its child records by batch_id
func (d *DatabaseService) GetBatchRecords(mainRecordID int) (*TrendsRecord, []TrendsRecord, error) {
	// First get the main record
	mainRecord, err := d.GetTrendsRecordByID(mainRecordID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get main record: %w", err)
	}

	// If this record doesn't have a batch_id, return just the main record
	if mainRecord.BatchID == "" {
		return mainRecord, []TrendsRecord{}, nil
	}

	// Get all child records with the same batch_id but different from main record
	query := `
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date, batch_id, parent_task_id, raw_data
		FROM trends_data
		WHERE batch_id = ? AND id != ?
		ORDER BY executed_at ASC, id ASC
	`

	rows, err := d.db.Query(query, mainRecord.BatchID, mainRecordID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to query child records: %w", err)
	}
	defer rows.Close()

	var childRecords []TrendsRecord
	for rows.Next() {
		var record TrendsRecord
		var rawDataJSON string
		var batchID, parentTaskID sql.NullString

		err := rows.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
			&record.DateRange, &record.DataType, &record.ExecutedAt,
			&record.AvgInterest, &record.DataPoints, &record.PeakDate, &batchID, &parentTaskID, &rawDataJSON)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to scan child record: %w", err)
		}

		// Handle NULL values
		if batchID.Valid {
			record.BatchID = batchID.String
		}
		if parentTaskID.Valid {
			record.ParentTaskID = parentTaskID.String
		}

		// Deserialize raw data
		var rawData models.TrendsResponse
		if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
			log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
		} else {
			record.RawData = &rawData
		}

		childRecords = append(childRecords, record)
	}

	return mainRecord, childRecords, nil
}

// GetTrendsRecordByID returns a specific trends record by ID
func (d *DatabaseService) GetTrendsRecordByID(id int) (*TrendsRecord, error) {
	query := `
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date, batch_id, parent_task_id, raw_data
		FROM trends_data
		WHERE id = ?
	`

	row := d.db.QueryRow(query, id)

	var record TrendsRecord
	var rawDataJSON string
	var batchID, parentTaskID sql.NullString

	err := row.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
		&record.DateRange, &record.DataType, &record.ExecutedAt,
		&record.AvgInterest, &record.DataPoints, &record.PeakDate, &batchID, &parentTaskID, &rawDataJSON)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("record with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get trends record by ID: %w", err)
	}

	// Handle NULL values
	if batchID.Valid {
		record.BatchID = batchID.String
	}
	if parentTaskID.Valid {
		record.ParentTaskID = parentTaskID.String
	}

	// Deserialize raw data
	var rawData models.TrendsResponse
	if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
		log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
	} else {
		record.RawData = &rawData
	}

	return &record, nil
}

// GetTrendsRecordsByIDs returns multiple trends records by their IDs
func (d *DatabaseService) GetTrendsRecordsByIDs(recordIDs []int) ([]TrendsRecord, error) {
	if len(recordIDs) == 0 {
		return []TrendsRecord{}, nil
	}

	// Build placeholders for IN clause
	placeholders := make([]string, len(recordIDs))
	args := make([]interface{}, len(recordIDs))
	for i, id := range recordIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	query := fmt.Sprintf(`
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date, raw_data
		FROM trends_data
		WHERE id IN (%s)
		ORDER BY executed_at DESC
	`, strings.Join(placeholders, ","))

	rows, err := d.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query records by IDs: %w", err)
	}
	defer rows.Close()

	var records []TrendsRecord
	for rows.Next() {
		var record TrendsRecord
		var rawDataJSON string

		err := rows.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
			&record.DateRange, &record.DataType, &record.ExecutedAt,
			&record.AvgInterest, &record.DataPoints, &record.PeakDate, &rawDataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trends record: %w", err)
		}

		// Deserialize raw data
		var rawData models.TrendsResponse
		if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
			log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
		} else {
			record.RawData = &rawData
		}

		records = append(records, record)
	}

	return records, nil
}

// DeleteTrendsRecord deletes a specific trends record and all related records in the same batch
func (d *DatabaseService) DeleteTrendsRecord(id int) error {
	// First get the record to check if it has a batch_id
	record, err := d.GetTrendsRecordByID(id)
	if err != nil {
		return fmt.Errorf("failed to get record: %w", err)
	}

	var totalDeleted int64

	// If the record has a batch_id, delete all records in the same batch
	if record.BatchID != "" {
		query := "DELETE FROM trends_data WHERE batch_id = ?"
		result, err := d.db.Exec(query, record.BatchID)
		if err != nil {
			return fmt.Errorf("failed to delete batch records: %w", err)
		}
		totalDeleted, err = result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}
		log.Printf("🗑️ Deleted %d records in batch: %s", totalDeleted, record.BatchID)
	} else {
		// If no batch_id, just delete the single record
		query := "DELETE FROM trends_data WHERE id = ?"
		result, err := d.db.Exec(query, id)
		if err != nil {
			return fmt.Errorf("failed to delete trends record: %w", err)
		}
		totalDeleted, err = result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}
		log.Printf("🗑️ Deleted trends record with ID: %d", id)
	}

	if totalDeleted == 0 {
		return fmt.Errorf("record with ID %d not found", id)
	}

	return nil
}

// DeleteTrendsRecords deletes multiple trends records and all related records in their batches
func (d *DatabaseService) DeleteTrendsRecords(ids []int) (int, error) {
	if len(ids) == 0 {
		return 0, fmt.Errorf("no IDs provided")
	}

	var totalDeleted int64

	// For each ID, delete the entire batch if it has a batch_id
	for _, id := range ids {
		record, err := d.GetTrendsRecordByID(id)
		if err != nil {
			log.Printf("⚠️ Warning: failed to get record %d: %v", id, err)
			continue
		}

		if record.BatchID != "" {
			// Delete all records in the same batch
			query := "DELETE FROM trends_data WHERE batch_id = ?"
			result, err := d.db.Exec(query, record.BatchID)
			if err != nil {
				log.Printf("⚠️ Warning: failed to delete batch %s: %v", record.BatchID, err)
				continue
			}
			batchDeleted, err := result.RowsAffected()
			if err != nil {
				log.Printf("⚠️ Warning: failed to get rows affected for batch %s: %v", record.BatchID, err)
				continue
			}
			totalDeleted += batchDeleted
			log.Printf("🗑️ Deleted %d records in batch: %s", batchDeleted, record.BatchID)
		} else {
			// Delete single record
			query := "DELETE FROM trends_data WHERE id = ?"
			result, err := d.db.Exec(query, id)
			if err != nil {
				log.Printf("⚠️ Warning: failed to delete record %d: %v", id, err)
				continue
			}
			singleDeleted, err := result.RowsAffected()
			if err != nil {
				log.Printf("⚠️ Warning: failed to get rows affected for record %d: %v", id, err)
				continue
			}
			totalDeleted += singleDeleted
			log.Printf("🗑️ Deleted record with ID: %d", id)
		}
	}

	deleted := int(totalDeleted)
	log.Printf("🗑️ Total deleted %d trends records", deleted)
	return deleted, nil
}

// GetDistinctTaskGroups returns all distinct task groups from the database
func (d *DatabaseService) GetDistinctTaskGroups() ([]string, error) {
	query := `
		SELECT DISTINCT task_group
		FROM trends_data
		WHERE task_group IS NOT NULL AND task_group != ''
		ORDER BY task_group
	`

	rows, err := d.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query distinct task groups: %w", err)
	}
	defer rows.Close()

	var taskGroups []string
	for rows.Next() {
		var taskGroup string
		if err := rows.Scan(&taskGroup); err != nil {
			return nil, fmt.Errorf("failed to scan task group: %w", err)
		}
		taskGroups = append(taskGroups, taskGroup)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating task groups: %w", err)
	}

	return taskGroups, nil
}

// ExportTrendsData exports trends data in specified format
func (d *DatabaseService) ExportTrendsData(taskGroup, dataType, geo string, days int, format string) ([]byte, error) {
	// Build query with filters
	query := `
		SELECT id, task_group, query, geo, date_range, data_type,
			   executed_at, avg_interest, data_points, peak_date
		FROM trends_data
		WHERE executed_at >= datetime('now', '-' || ? || ' days')
	`
	args := []interface{}{days}

	if taskGroup != "" {
		query += " AND task_group = ?"
		args = append(args, taskGroup)
	}
	if dataType != "" {
		query += " AND data_type = ?"
		args = append(args, dataType)
	}
	if geo != "" {
		query += " AND geo = ?"
		args = append(args, geo)
	}

	query += " ORDER BY executed_at DESC"

	rows, err := d.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query trends data for export: %w", err)
	}
	defer rows.Close()

	switch format {
	case "csv":
		return d.exportToCSV(rows)
	case "json":
		return d.exportToJSON(rows)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}
}

// GetExecutionStats returns execution statistics
func (d *DatabaseService) GetExecutionStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total records
	var totalRecords int
	err := d.db.QueryRow("SELECT COUNT(*) FROM trends_data").Scan(&totalRecords)
	if err != nil {
		return nil, fmt.Errorf("failed to get total records: %w", err)
	}
	stats["total_records"] = totalRecords

	// Records today
	var recordsToday int
	err = d.db.QueryRow("SELECT COUNT(*) FROM trends_data WHERE date(executed_at) = date('now')").Scan(&recordsToday)
	if err != nil {
		return nil, fmt.Errorf("failed to get today's records: %w", err)
	}
	stats["records_today"] = recordsToday

	// Last execution
	var lastExecution sql.NullString
	err = d.db.QueryRow("SELECT MAX(executed_at) FROM trends_data").Scan(&lastExecution)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to get last execution: %w", err)
	}
	if lastExecution.Valid {
		stats["last_execution"] = lastExecution.String
	} else {
		stats["last_execution"] = "Never"
	}

	// Unique queries
	var uniqueQueries int
	err = d.db.QueryRow("SELECT COUNT(DISTINCT query) FROM trends_data").Scan(&uniqueQueries)
	if err != nil {
		return nil, fmt.Errorf("failed to get unique queries: %w", err)
	}
	stats["unique_queries"] = uniqueQueries

	return stats, nil
}

// exportToCSV exports query results to CSV format
func (d *DatabaseService) exportToCSV(rows *sql.Rows) ([]byte, error) {
	var buffer bytes.Buffer
	writer := csv.NewWriter(&buffer)

	// Write header
	header := []string{"ID", "Task Group", "Query", "Geo", "Date Range", "Data Type",
		"Executed At", "Avg Interest", "Data Points", "Peak Date"}
	if err := writer.Write(header); err != nil {
		return nil, fmt.Errorf("failed to write CSV header: %w", err)
	}

	// Write data rows
	for rows.Next() {
		var id, avgInterest, dataPoints int
		var taskGroup, query, geo, dateRange, dataType, executedAt, peakDate string

		err := rows.Scan(&id, &taskGroup, &query, &geo, &dateRange, &dataType,
			&executedAt, &avgInterest, &dataPoints, &peakDate)
		if err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		record := []string{
			strconv.Itoa(id),
			taskGroup,
			query,
			geo,
			dateRange,
			dataType,
			executedAt,
			strconv.Itoa(avgInterest),
			strconv.Itoa(dataPoints),
			peakDate,
		}

		if err := writer.Write(record); err != nil {
			return nil, fmt.Errorf("failed to write CSV record: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("CSV writer error: %w", err)
	}

	return buffer.Bytes(), nil
}

// CleanupOldData removes data older than specified days
func (d *DatabaseService) CleanupOldData(retentionDays int) (int, error) {
	if retentionDays <= 0 {
		return 0, fmt.Errorf("retention days must be positive")
	}

	log.Printf("🧹 Starting data cleanup - removing data older than %d days", retentionDays)

	// Delete trends data older than retention period
	query := `
		DELETE FROM trends_data
		WHERE executed_at < datetime('now', '-' || ? || ' days')
	`

	result, err := d.db.Exec(query, retentionDays)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup trends data: %w", err)
	}

	deletedCount, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get deleted count: %w", err)
	}

	// Also cleanup execution logs older than retention period
	logQuery := `
		DELETE FROM execution_logs
		WHERE executed_at < datetime('now', '-' || ? || ' days')
	`

	logResult, err := d.db.Exec(logQuery, retentionDays)
	if err != nil {
		log.Printf("⚠️  Failed to cleanup execution logs: %v", err)
	} else {
		logDeletedCount, _ := logResult.RowsAffected()
		log.Printf("🧹 Cleaned up %d execution log entries", logDeletedCount)
	}

	log.Printf("🧹 Data cleanup completed - removed %d trends records older than %d days", deletedCount, retentionDays)
	return int(deletedCount), nil
}

// GetDataStats returns statistics about data storage
func (d *DatabaseService) GetDataStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total records
	var totalRecords int
	err := d.db.QueryRow("SELECT COUNT(*) FROM trends_data").Scan(&totalRecords)
	if err != nil {
		return nil, fmt.Errorf("failed to get total records: %w", err)
	}
	stats["total_records"] = totalRecords

	// Records by age
	var recordsLast24h, recordsLast7d, recordsLast30d int

	err = d.db.QueryRow("SELECT COUNT(*) FROM trends_data WHERE executed_at >= datetime('now', '-1 days')").Scan(&recordsLast24h)
	if err != nil {
		return nil, fmt.Errorf("failed to get 24h records: %w", err)
	}
	stats["records_last_24h"] = recordsLast24h

	err = d.db.QueryRow("SELECT COUNT(*) FROM trends_data WHERE executed_at >= datetime('now', '-7 days')").Scan(&recordsLast7d)
	if err != nil {
		return nil, fmt.Errorf("failed to get 7d records: %w", err)
	}
	stats["records_last_7d"] = recordsLast7d

	err = d.db.QueryRow("SELECT COUNT(*) FROM trends_data WHERE executed_at >= datetime('now', '-30 days')").Scan(&recordsLast30d)
	if err != nil {
		return nil, fmt.Errorf("failed to get 30d records: %w", err)
	}
	stats["records_last_30d"] = recordsLast30d

	// Oldest and newest records
	var oldestDate, newestDate string
	err = d.db.QueryRow("SELECT MIN(executed_at), MAX(executed_at) FROM trends_data").Scan(&oldestDate, &newestDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get date range: %w", err)
	}
	stats["oldest_record"] = oldestDate
	stats["newest_record"] = newestDate

	// Database size (approximate)
	var pageCount, pageSize int
	err = d.db.QueryRow("PRAGMA page_count").Scan(&pageCount)
	if err == nil {
		err = d.db.QueryRow("PRAGMA page_size").Scan(&pageSize)
		if err == nil {
			stats["database_size_bytes"] = pageCount * pageSize
			stats["database_size_mb"] = float64(pageCount*pageSize) / (1024 * 1024)
		}
	}

	return stats, nil
}

// exportToJSON exports query results to JSON format
func (d *DatabaseService) exportToJSON(rows *sql.Rows) ([]byte, error) {
	var records []map[string]interface{}

	for rows.Next() {
		var id, avgInterest, dataPoints int
		var taskGroup, query, geo, dateRange, dataType, executedAt, peakDate string

		err := rows.Scan(&id, &taskGroup, &query, &geo, &dateRange, &dataType,
			&executedAt, &avgInterest, &dataPoints, &peakDate)
		if err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		record := map[string]interface{}{
			"id":           id,
			"task_group":   taskGroup,
			"query":        query,
			"geo":          geo,
			"date_range":   dateRange,
			"data_type":    dataType,
			"executed_at":  executedAt,
			"avg_interest": avgInterest,
			"data_points":  dataPoints,
			"peak_date":    peakDate,
		}

		records = append(records, record)
	}

	data, err := json.MarshalIndent(records, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}

	return data, nil
}

// Close closes the database connection
func (d *DatabaseService) Close() error {
	if d.db != nil {
		return d.db.Close()
	}
	return nil
}
