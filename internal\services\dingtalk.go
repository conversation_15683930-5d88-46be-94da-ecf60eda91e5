package services

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sort"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/models"
)

// DingTalkService handles DingTalk integration
type DingTalkService struct {
	config        *config.DingTalkConfig
	database      *DatabaseService
	httpClient    *http.Client
	streamBot     *DingTalkStreamBot
	geminiService *GeminiService
}

// DingTalkRecipient represents a DingTalk user or group
type DingTalkRecipient struct {
	ID            int       `json:"id"`
	RecipientID   string    `json:"recipient_id"`
	RecipientType string    `json:"recipient_type"` // "user" or "group"
	Name          string    `json:"name"`
	Enabled       bool      `json:"enabled"`
	CreatedAt     time.Time `json:"created_at"`
	LastActiveAt  time.Time `json:"last_active_at"`
}

// DingTalkMessage represents a formatted message for DingTalk
type DingTalkMessage struct {
	Title       string `json:"title"`
	Content     string `json:"content"`
	MessageType string `json:"message_type"` // "text", "markdown", "actionCard"
}

// NewDingTalkService creates a new DingTalk service
func NewDingTalkService(config *config.DingTalkConfig, database *DatabaseService) (*DingTalkService, error) {
	if !config.BotEnabled {
		log.Println("📱 DingTalk service is disabled")
		geminiService, err := NewGeminiService()
		if err != nil {
			log.Printf("⚠️  Failed to initialize Gemini service: %v", err)
		}

		return &DingTalkService{
			config:        config,
			database:      database,
			httpClient:    &http.Client{Timeout: time.Duration(config.TimeoutSeconds) * time.Second},
			geminiService: geminiService,
		}, nil
	}

	// Initialize HTTP client
	httpClient := &http.Client{
		Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
	}

	geminiService, err := NewGeminiService()
	if err != nil {
		log.Printf("⚠️  Failed to initialize Gemini service: %v", err)
	}

	service := &DingTalkService{
		config:        config,
		database:      database,
		httpClient:    httpClient,
		geminiService: geminiService,
	}

	// Initialize Stream bot
	service.streamBot = NewDingTalkStreamBot(config, service)

	// Initialize recipients from config
	err = service.initializeRecipients()
	if err != nil {
		log.Printf("⚠️  Failed to initialize recipients: %v", err)
	}

	log.Printf("📱 DingTalk service initialized - Bot: %s", config.BotName)
	return service, nil
}

// initializeRecipients initializes recipients from configuration
func (d *DingTalkService) initializeRecipients() error {
	// Parse predefined users and groups from config
	predefinedUsers := d.parseUsersFromConfig()
	predefinedGroups := d.parseGroupsFromConfig()

	log.Printf("📱 Initialized recipients from config: %d users, %d groups",
		len(predefinedUsers), len(predefinedGroups))

	// Log loaded users
	for userID, userName := range predefinedUsers {
		log.Printf("✅ Loaded user from config: %s (%s)", userName, userID)
	}

	// Log loaded groups
	for groupID, groupName := range predefinedGroups {
		log.Printf("✅ Loaded group from config: %s (%s)", groupName, groupID)
	}

	return nil
}

// parseUsersFromConfig parses users from configuration
func (d *DingTalkService) parseUsersFromConfig() map[string]string {
	users := make(map[string]string)

	// Parse from DINGTALK_USERS
	// Format: "userID1:userName1,userID2:userName2"
	if d.config.Users != "" {
		userPairs := strings.Split(d.config.Users, ",")
		for _, pair := range userPairs {
			parts := strings.SplitN(strings.TrimSpace(pair), ":", 2)
			if len(parts) == 2 {
				userID := strings.TrimSpace(parts[0])
				userName := strings.TrimSpace(parts[1])
				users[userID] = userName
				log.Printf("📱 Parsed user from config: %s -> %s", userID, userName)
			}
		}
	}

	return users
}

// parseGroupsFromConfig parses groups from configuration
func (d *DingTalkService) parseGroupsFromConfig() map[string]string {
	groups := make(map[string]string)

	// Parse from DINGTALK_GROUPS
	// Format: "groupID1:groupName1|groupID2:groupName2" (using | to avoid comma conflicts)
	if d.config.Groups != "" {
		groupPairs := strings.Split(d.config.Groups, "|")
		for _, pair := range groupPairs {
			parts := strings.SplitN(strings.TrimSpace(pair), ":", 2)
			if len(parts) == 2 {
				groupID := strings.TrimSpace(parts[0])
				groupName := strings.TrimSpace(parts[1])
				groups[groupID] = groupName
				log.Printf("📱 Parsed group from config: %s -> %s", groupID, groupName)
			}
		}
	}

	return groups
}

// GetTestUserID returns the test user ID from config
func (d *DingTalkService) GetTestUserID() string {
	return d.config.DefaultTestUser
}

// GetTestGroupID returns the test group ID from config
func (d *DingTalkService) GetTestGroupID() string {
	return d.config.DefaultTestGroup
}

// StartMessageListener starts listening for incoming messages using real Stream mode
func (d *DingTalkService) StartMessageListener() error {
	if !d.config.BotEnabled {
		log.Println("📱 DingTalk bot is disabled, skipping message listener")
		return nil
	}

	log.Println("📱 Starting DingTalk Stream mode message listener...")
	log.Println("📱 ⚠️  重要配置说明:")
	log.Println("📱 1. 钉钉机器人需要在开发者后台配置为Stream模式")
	log.Println("📱 2. 需要开启'接收消息'权限")
	log.Println("📱 3. 配置步骤:")
	log.Println("📱    - 登录钉钉开发者后台: https://open-dev.dingtalk.com")
	log.Println("📱    - 找到你的机器人应用")
	log.Println("📱    - 在'机器人配置'中选择'Stream模式'")
	log.Println("📱    - 开启'接收消息'权限")
	log.Println("📱    - 发布应用")

	// 启动真正的Stream机器人
	go func() {
		ctx := context.Background()
		if err := d.streamBot.Start(ctx); err != nil {
			log.Printf("❌ Stream机器人启动失败: %v", err)
		}
	}()

	log.Println("📱 ✅ Real Stream mode bot started")
	return nil
}

// HandleIncomingMessage handles incoming messages from users/groups
func (d *DingTalkService) HandleIncomingMessage(senderID, senderName, conversationID, conversationType, messageContent string) error {
	log.Printf("📱 ========== 开始处理钉钉消息 ==========")
	log.Printf("📱 发送者: %s (%s)", senderName, senderID)
	log.Printf("📱 对话ID: %s", conversationID)
	log.Printf("📱 对话类型: %s (1=单聊, 2=群聊)", conversationType)
	log.Printf("📱 消息内容: %s", messageContent)

	// 自动收集用户/群组ID
	log.Printf("📱 开始收集发送者信息...")
	err := d.collectSenderInfo(senderID, senderName, conversationID, conversationType)
	if err != nil {
		log.Printf("⚠️  Failed to collect sender info: %v", err)
	} else {
		log.Printf("✅ 发送者信息收集成功")
	}

	// 发送自动回复
	log.Printf("📱 开始发送自动回复...")
	replyErr := d.sendAutoReply(senderID, senderName, conversationType, conversationID)
	if replyErr != nil {
		log.Printf("❌ 自动回复发送失败: %v", replyErr)
	} else {
		log.Printf("✅ 自动回复发送成功")
	}

	log.Printf("📱 ========== 消息处理完成 ==========")
	return replyErr
}

// collectSenderInfo collects sender information to ID configuration file
func (d *DingTalkService) collectSenderInfo(senderID, senderName, conversationID, conversationType string) error {
	// 确定是用户还是群组
	var idType, targetID, targetName string

	if conversationType == "1" { // 单聊
		idType = "user"
		targetID = senderID
		targetName = senderName
		log.Printf("📱 识别为单聊消息，用户ID: %s, 用户名: %s", targetID, targetName)
	} else { // 群聊
		idType = "group"
		targetID = conversationID
		targetName = senderName + "的群组" // 可以后续优化获取真实群名
		log.Printf("📱 识别为群聊消息，群组ID: %s, 群组名: %s, 发送者: %s", targetID, targetName, senderName)
	}

	// 保存到ID配置文件
	log.Printf("📱 保存%s信息到配置文件: %s -> %s", idType, targetID, targetName)
	return d.SaveToIDConfigFile(idType, targetID, targetName)
}

// SaveToIDConfigFile saves ID information to configuration file (public method)
func (d *DingTalkService) SaveToIDConfigFile(idType, id, name string) error {
	filename := "dingtalk_ids.txt"
	log.Printf("📱 保存ID到文件: %s, 类型: %s, ID: %s, 名称: %s", filename, idType, id, name)

	// 读取现有内容
	existingIDs, err := d.readIDConfigFile(filename)
	if err != nil {
		log.Printf("⚠️  Failed to read existing ID file: %v", err)
		existingIDs = make(map[string]string)
	} else {
		log.Printf("📱 成功读取现有ID文件，已有 %d 个ID", len(existingIDs))
	}

	// 检查ID是否已存在
	if existingName, exists := existingIDs[id]; exists {
		if existingName != name {
			log.Printf("📱 更新%s %s的名称: %s -> %s", idType, id, existingName, name)
		} else {
			log.Printf("📱 %s %s (%s) 已存在，跳过保存", idType, id, name)
			return nil
		}
	} else {
		log.Printf("📱 发现新的%s: %s (%s)", idType, id, name)
	}

	// 添加新ID
	existingIDs[id] = name

	// 写回文件
	err = d.writeIDConfigFile(filename, existingIDs)
	if err != nil {
		log.Printf("❌ 保存ID文件失败: %v", err)
		return err
	}

	log.Printf("✅ 成功保存%s信息到文件: %s (%s)", idType, id, name)
	return nil
}

// sendAutoReply sends automatic reply to the sender
func (d *DingTalkService) sendAutoReply(senderID, senderName, conversationType, conversationID string) error {
	log.Printf("📱 开始发送自动回复: sender=%s (%s), conversationType=%s, conversationID=%s",
		senderName, senderID, conversationType, conversationID)

	// 检查机器人是否启用
	if !d.config.BotEnabled {
		log.Println("📱 DingTalk bot is disabled, skipping auto reply")
		return nil
	}

	// 获取access token
	log.Printf("📱 获取access token...")
	accessToken, err := d.GetAccessToken()
	if err != nil {
		log.Printf("❌ Failed to get access token for auto reply: %v", err)
		return fmt.Errorf("failed to get access token: %w", err)
	}
	log.Printf("✅ Access token获取成功")

	replyMessage := &DingTalkMessage{
		Title:       fmt.Sprintf("🤖 %s", d.config.BotName),
		Content:     fmt.Sprintf("# 🤖 %s\n\n你好 %s！\n\n%s\n\n✅ 你的ID已被记录，可以接收推送消息了！\n\n📊 我会定期推送Google Trends数据给你。", d.config.BotName, senderName, d.config.BotDescription),
		MessageType: "markdown",
	}

	var targetID string
	var err2 error
	if conversationType == "1" { // 单聊
		targetID = senderID
		log.Printf("📱 发送自动回复给用户: %s (%s)", senderName, senderID)
		err2 = d.sendMessageToUser(accessToken, replyMessage, targetID)
	} else { // 群聊
		targetID = conversationID
		log.Printf("📱 发送自动回复给群组: %s (来自用户: %s)", conversationID, senderName)
		err2 = d.sendMessageToGroup(accessToken, replyMessage, targetID)
	}

	if err2 != nil {
		log.Printf("❌ 自动回复发送失败: %v", err2)
		return fmt.Errorf("failed to send auto reply: %w", err2)
	}

	log.Printf("✅ 自动回复发送成功给 %s", senderName)
	return nil
}

// readIDConfigFile reads existing ID configuration from file
func (d *DingTalkService) readIDConfigFile(filename string) (map[string]string, error) {
	ids := make(map[string]string)

	file, err := os.Open(filename)
	if err != nil {
		if os.IsNotExist(err) {
			return ids, nil // 文件不存在，返回空map
		}
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue // 跳过空行和注释
		}

		// 解析格式: ID:Name
		parts := strings.SplitN(line, ":", 2)
		if len(parts) == 2 {
			id := strings.TrimSpace(parts[0])
			name := strings.TrimSpace(parts[1])
			ids[id] = name
		}
	}

	return ids, scanner.Err()
}

// writeIDConfigFile writes ID configuration to file
func (d *DingTalkService) writeIDConfigFile(filename string, existingIDs map[string]string) error {
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)

	// 写入文件头
	fmt.Fprintf(writer, "# 钉钉用户和群组ID配置文件\n")
	fmt.Fprintf(writer, "# 格式: ID:名称\n")
	fmt.Fprintf(writer, "# 此文件由系统自动生成和更新\n\n")

	// 写入用户ID
	fmt.Fprintf(writer, "# 用户ID\n")
	for id, name := range existingIDs {
		// 判断是否为用户ID (纯数字格式)
		if !strings.HasPrefix(id, "cid") && !strings.Contains(id, "$:LWCP_v1:$") {
			fmt.Fprintf(writer, "%s:%s\n", id, name)
		}
	}

	fmt.Fprintf(writer, "\n# 群组ID\n")
	for id, name := range existingIDs {
		// 判断是否为群组ID (以cid开头)
		if strings.HasPrefix(id, "cid") {
			fmt.Fprintf(writer, "%s:%s\n", id, name)
		}
	}

	// 注意：不需要再次添加新ID，因为已经在existingIDs中了

	writer.Flush()
	log.Printf("📱 ID配置已保存到文件: %s", filename)
	return nil
}

// GetAccessToken gets access token from DingTalk API
func (d *DingTalkService) GetAccessToken() (string, error) {
	log.Printf("📱 ========== 获取钉钉Access Token ==========")

	url := "https://oapi.dingtalk.com/gettoken"
	log.Printf("📱 Token API URL: %s", url)

	// Build request parameters
	params := fmt.Sprintf("?appkey=%s&appsecret=%s", d.config.ClientID, d.config.ClientSecret)
	log.Printf("📱 ClientID: %s", d.config.ClientID)
	log.Printf("📱 ClientSecret: %s...", d.config.ClientSecret[:10]) // 只显示前10个字符

	fullURL := url + params
	log.Printf("📱 完整请求URL: %s", fullURL)

	log.Printf("📱 发送HTTP GET请求...")
	resp, err := d.httpClient.Get(fullURL)
	if err != nil {
		log.Printf("❌ HTTP请求失败: %v", err)
		return "", fmt.Errorf("failed to request access token: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("📱 HTTP响应状态: %s", resp.Status)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 读取响应体失败: %v", err)
		return "", fmt.Errorf("failed to read response: %w", err)
	}
	log.Printf("📱 响应体: %s", string(body))

	var tokenResp struct {
		ErrCode     int    `json:"errcode"`
		ErrMsg      string `json:"errmsg"`
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
	}

	if err := json.Unmarshal(body, &tokenResp); err != nil {
		log.Printf("❌ 解析Token响应失败: %v", err)
		return "", fmt.Errorf("failed to parse token response: %w", err)
	}

	log.Printf("📱 Token响应: errcode=%d, errmsg=%s, expires_in=%d",
		tokenResp.ErrCode, tokenResp.ErrMsg, tokenResp.ExpiresIn)

	if tokenResp.ErrCode != 0 {
		log.Printf("❌ 钉钉API返回错误: %d - %s", tokenResp.ErrCode, tokenResp.ErrMsg)
		return "", fmt.Errorf("DingTalk API error: %d - %s", tokenResp.ErrCode, tokenResp.ErrMsg)
	}

	if tokenResp.AccessToken == "" {
		log.Printf("❌ Access Token为空")
		return "", fmt.Errorf("received empty access token")
	}

	log.Printf("✅ 成功获取Access Token (有效期: %d秒), Token: %s...",
		tokenResp.ExpiresIn, tokenResp.AccessToken[:20])
	return tokenResp.AccessToken, nil
}

// sendMessageToRecipient sends a message to a specific recipient
func (d *DingTalkService) sendMessageToRecipient(accessToken string, message *DingTalkMessage, recipientID string) error {
	// Check if recipient is a user or group from config
	recipients, err := d.GetRecipientsFromConfig()
	if err != nil {
		return fmt.Errorf("failed to get recipients: %w", err)
	}

	var recipientType string
	for _, recipient := range recipients {
		if recipient.RecipientID == recipientID {
			recipientType = recipient.RecipientType
			break
		}
	}

	if recipientType == "" {
		return fmt.Errorf("recipient %s not found", recipientID)
	}

	// Send message based on recipient type
	if recipientType == "user" {
		return d.sendMessageToUser(accessToken, message, recipientID)
	} else {
		return d.sendMessageToGroup(accessToken, message, recipientID)
	}
}

// sendMessageToUser sends a message to a user (1v1 chat)
func (d *DingTalkService) sendMessageToUser(accessToken string, message *DingTalkMessage, userID string) error {
	log.Printf("📱 ========== 发送单聊消息 ==========")
	log.Printf("📱 目标用户ID: %s", userID)
	log.Printf("📱 消息标题: %s", message.Title)
	log.Printf("📱 消息类型: %s", message.MessageType)

	// 使用机器人发送单聊消息的API
	url := "https://api.dingtalk.com/v1.0/robot/oToMessages/batchSend"
	log.Printf("📱 API URL: %s", url)

	// Build msgParam as JSON string (required by DingTalk API)
	msgParam := map[string]interface{}{
		"title": message.Title,
		"text":  message.Content,
	}
	msgParamJSON, err := json.Marshal(msgParam)
	if err != nil {
		log.Printf("❌ 构建msgParam失败: %v", err)
		return fmt.Errorf("failed to marshal msgParam: %w", err)
	}

	// Build message payload for 1v1 chat
	payload := map[string]interface{}{
		"robotCode": d.config.ClientID, // 使用机器人代码
		"userIds":   []string{userID},
		"msgKey":    "sampleMarkdown",
		"msgParam":  string(msgParamJSON), // msgParam must be a JSON string
	}

	log.Printf("📱 机器人代码: %s", d.config.ClientID)
	log.Printf("📱 消息参数: %s", string(msgParamJSON))

	err = d.sendDingTalkRequestV2(url, accessToken, payload)
	if err != nil {
		log.Printf("❌ 单聊消息发送失败: %v", err)
	} else {
		log.Printf("✅ 单聊消息发送成功")
	}
	return err
}

// sendMessageToGroup sends a message to a group
func (d *DingTalkService) sendMessageToGroup(accessToken string, message *DingTalkMessage, groupID string) error {
	log.Printf("📱 ========== 发送群聊消息 ==========")
	log.Printf("📱 目标群组ID: %s", groupID)
	log.Printf("📱 消息标题: %s", message.Title)
	log.Printf("📱 消息类型: %s", message.MessageType)

	// 使用机器人发送群聊消息的API
	url := "https://api.dingtalk.com/v1.0/robot/groupMessages/send"
	log.Printf("📱 API URL: %s", url)

	// Build msgParam as JSON string (required by DingTalk API)
	msgParam := map[string]interface{}{
		"title": message.Title,
		"text":  message.Content,
	}
	msgParamJSON, err := json.Marshal(msgParam)
	if err != nil {
		log.Printf("❌ 构建msgParam失败: %v", err)
		return fmt.Errorf("failed to marshal msgParam: %w", err)
	}

	// Build message payload for group chat
	payload := map[string]interface{}{
		"robotCode":          d.config.ClientID, // 使用机器人代码
		"openConversationId": groupID,
		"msgKey":             "sampleMarkdown",
		"msgParam":           string(msgParamJSON), // msgParam must be a JSON string
	}

	log.Printf("📱 机器人代码: %s", d.config.ClientID)
	log.Printf("📱 消息参数: %s", string(msgParamJSON))

	err = d.sendDingTalkRequestV2(url, accessToken, payload)
	if err != nil {
		log.Printf("❌ 群聊消息发送失败: %v", err)
	} else {
		log.Printf("✅ 群聊消息发送成功")
	}
	return err
}

// sendDingTalkRequestV2 sends a request to DingTalk API v2.0 (for robot messages)
func (d *DingTalkService) sendDingTalkRequestV2(url, accessToken string, payload map[string]interface{}) error {
	log.Printf("📱 ========== 发送钉钉API请求 ==========")
	log.Printf("📱 API URL: %s", url)
	log.Printf("📱 Access Token: %s...", accessToken[:20]) // 只显示前20个字符

	jsonData, err := json.Marshal(payload)
	if err != nil {
		log.Printf("❌ 序列化请求数据失败: %v", err)
		return fmt.Errorf("failed to marshal payload: %w", err)
	}
	log.Printf("📱 请求数据: %s", string(jsonData))

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("❌ 创建HTTP请求失败: %v", err)
		return fmt.Errorf("failed to create request: %w", err)
	}

	// v2.0 API uses Bearer token in header
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-acs-dingtalk-access-token", accessToken)
	log.Printf("📱 设置请求头: Content-Type=application/json, x-acs-dingtalk-access-token=***")

	log.Printf("📱 发送HTTP请求...")
	resp, err := d.httpClient.Do(req)
	if err != nil {
		log.Printf("❌ HTTP请求发送失败: %v", err)
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("📱 HTTP响应状态: %s", resp.Status)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 读取响应体失败: %v", err)
		return fmt.Errorf("failed to read response: %w", err)
	}
	log.Printf("📱 响应体: %s", string(body))

	var result struct {
		Code      string `json:"code"`
		Message   string `json:"message"`
		RequestID string `json:"requestId"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		log.Printf("❌ 解析响应失败: %v", err)
		return fmt.Errorf("failed to parse response: %w", err)
	}

	log.Printf("📱 API响应: code=%s, message=%s, requestId=%s", result.Code, result.Message, result.RequestID)

	if result.Code != "" && result.Code != "200" {
		log.Printf("❌ 钉钉API返回错误: %s - %s", result.Code, result.Message)
		return fmt.Errorf("DingTalk API v2.0 error: %s - %s", result.Code, result.Message)
	}

	log.Printf("✅ 钉钉API调用成功, request_id: %s", result.RequestID)
	return nil
}

// AddRecipient is deprecated - recipients are now managed through configuration file
// This method is kept for compatibility but does nothing
func (d *DingTalkService) AddRecipient(recipientID, recipientType, name string) error {
	log.Printf("📱 AddRecipient called but ignored (using config file): %s (%s) - %s", recipientID, recipientType, name)
	return nil
}

// GetRecipients returns all enabled recipients from database
func (d *DingTalkService) GetRecipients() ([]DingTalkRecipient, error) {
	if d.database == nil {
		return nil, fmt.Errorf("database not available")
	}

	query := `
		SELECT id, recipient_id, recipient_type, name, enabled, created_at, last_active_at
		FROM dingtalk_recipients
		WHERE enabled = 1
		ORDER BY recipient_type, name
	`

	rows, err := d.database.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query recipients: %w", err)
	}
	defer rows.Close()

	var recipients []DingTalkRecipient
	for rows.Next() {
		var recipient DingTalkRecipient
		var enabled int

		err := rows.Scan(&recipient.ID, &recipient.RecipientID, &recipient.RecipientType,
			&recipient.Name, &enabled, &recipient.CreatedAt, &recipient.LastActiveAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan recipient: %w", err)
		}

		recipient.Enabled = enabled == 1
		recipients = append(recipients, recipient)
	}

	return recipients, nil
}

// GetRecipientsFromConfig returns all recipients from configuration file
func (d *DingTalkService) GetRecipientsFromConfig() ([]DingTalkRecipient, error) {
	var recipients []DingTalkRecipient

	// Parse users from config
	users := d.parseUsersFromConfig()
	for userID, userName := range users {
		recipient := DingTalkRecipient{
			ID:            0, // Config-based recipients don't have database IDs
			RecipientID:   userID,
			RecipientType: "user",
			Name:          userName,
			Enabled:       true,
			CreatedAt:     time.Now(),
			LastActiveAt:  time.Now(),
		}
		recipients = append(recipients, recipient)
	}

	// Parse groups from config
	groups := d.parseGroupsFromConfig()
	for groupID, groupName := range groups {
		recipient := DingTalkRecipient{
			ID:            0, // Config-based recipients don't have database IDs
			RecipientID:   groupID,
			RecipientType: "group",
			Name:          groupName,
			Enabled:       true,
			CreatedAt:     time.Now(),
			LastActiveAt:  time.Now(),
		}
		recipients = append(recipients, recipient)
	}

	// Sort recipients by type and name
	sort.Slice(recipients, func(i, j int) bool {
		if recipients[i].RecipientType != recipients[j].RecipientType {
			return recipients[i].RecipientType < recipients[j].RecipientType
		}
		return recipients[i].Name < recipients[j].Name
	})

	log.Printf("📱 Loaded %d recipients from config: %d users, %d groups",
		len(recipients), len(users), len(groups))

	return recipients, nil
}

// parseTaskGroupInfo extracts region and type from task group name
func (d *DingTalkService) parseTaskGroupInfo(taskGroupName string) (region, taskType string) {
	// Default values
	region = "未知地区"
	taskType = "未知类型"

	// Parse patterns like "US_Trending_Now", "China_Explore", "HongKong_Trending_Now"
	parts := strings.Split(taskGroupName, "_")
	if len(parts) >= 2 {
		region = parts[0]
		if len(parts) >= 3 {
			taskType = strings.Join(parts[1:], "_")
		} else {
			taskType = parts[1]
		}

		// Convert to Chinese display names
		switch region {
		case "US":
			region = "美国"
		case "China":
			region = "中国"
		case "HongKong":
			region = "香港"
		case "CN":
			region = "中国"
		case "HK":
			region = "香港"
		}

		switch taskType {
		case "Trending_Now":
			taskType = "实时热门"
		case "Explore":
			taskType = "探索热门"
		}
	}

	return region, taskType
}

// FormatTrendsMessage formats trends data into a DingTalk message
func (d *DingTalkService) FormatTrendsMessage(taskGroupName string, records []TrendsRecord) *DingTalkMessage {
	if len(records) == 0 {
		return &DingTalkMessage{
			Title:       fmt.Sprintf("%s - 无数据", d.config.MessageTitlePrefix),
			Content:     "暂无趋势数据",
			MessageType: "text",
		}
	}

	var content strings.Builder

	// Simple title: just use the configured prefix
	title := d.config.MessageTitlePrefix
	content.WriteString(fmt.Sprintf("# %s\n\n", title))

	// Time info
	content.WriteString(fmt.Sprintf("📅 **数据时间**: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	// Group records by task group for organized display
	taskGroups := make(map[string][]TrendsRecord)
	for _, record := range records {
		taskGroups[record.TaskGroup] = append(taskGroups[record.TaskGroup], record)
	}

	// Display each task group separately
	for taskGroup, taskRecords := range taskGroups {
		d.formatTaskGroupData(&content, taskGroup, taskRecords)
	}

	// Add Gemini analysis if enabled (only for push messages, not for auto-replies)
	log.Printf("🔍 Debug: geminiService=%v, records count=%d", d.geminiService != nil, len(records))
	if d.geminiService != nil && len(records) > 0 {
		log.Println("🤖 Starting Gemini analysis...")
		analysis, err := d.geminiService.AnalyzeTrendsData(content.String())
		if err != nil {
			log.Printf("⚠️  Gemini analysis failed: %v", err)
		} else if analysis != "" {
			log.Printf("✅ Gemini analysis completed, length: %d", len(analysis))
			// Use configurable analysis title
			if d.geminiService.config != nil {
				content.WriteString(fmt.Sprintf("## %s\n\n", d.geminiService.config.AnalysisTitle))
			} else {
				content.WriteString("## 🧠 AI 趋势洞察\n\n")
			}
			content.WriteString(analysis)
			content.WriteString("\n\n")
		} else {
			log.Println("⚠️  Gemini analysis returned empty result")
		}
	} else {
		log.Printf("⚠️  Gemini analysis skipped - service: %v, records: %d", d.geminiService != nil, len(records))
	}

	// Footer
	content.WriteString("---\n")
	content.WriteString(d.config.BotDescription)

	return &DingTalkMessage{
		Title:       title,
		Content:     content.String(),
		MessageType: "markdown",
	}
}

// formatTaskGroupData formats data for a single task group
func (d *DingTalkService) formatTaskGroupData(content *strings.Builder, taskGroup string, records []TrendsRecord) {
	if len(records) == 0 {
		return
	}

	// Parse task group info for display
	region, taskType := d.parseTaskGroupInfo(taskGroup)

	// Task group header: 地区|类型
	content.WriteString(fmt.Sprintf("## %s|%s\n\n", region, taskType))

	// Group records by data type (corrected logic)
	exploreRecords := []TrendsRecord{}
	trendingRecords := []TrendsRecord{}
	relatedQueriesRecords := []TrendsRecord{}
	relatedTopicsRecords := []TrendsRecord{}

	for _, record := range records {
		switch record.DataType {
		case "EXPLORE", "EXPLORE_TOPIC":
			exploreRecords = append(exploreRecords, record)
		case "TRENDING_ITEM":
			// Filter out trending_now placeholder records
			if record.Query != "trending_now" {
				trendingRecords = append(trendingRecords, record)
			}
		case "RELATED_QUERIES":
			relatedQueriesRecords = append(relatedQueriesRecords, record)
		case "RELATED_TOPICS":
			relatedTopicsRecords = append(relatedTopicsRecords, record)
		}
	}

	// Display EXPLORE data
	if len(exploreRecords) > 0 {
		d.formatExploreData(content, exploreRecords)
	}

	// Display TRENDING_NOW data
	if len(trendingRecords) > 0 {
		d.formatTrendingData(content, trendingRecords)
	}

	// Display RELATED_QUERIES data
	if len(relatedQueriesRecords) > 0 {
		d.formatRelatedQueriesData(content, relatedQueriesRecords)
	}

	// Display RELATED_TOPICS data
	if len(relatedTopicsRecords) > 0 {
		d.formatRelatedTopicsData(content, relatedTopicsRecords)
	}

	content.WriteString("\n")
}

// formatExploreData formats EXPLORE type data
func (d *DingTalkService) formatExploreData(content *strings.Builder, records []TrendsRecord) {
	// Separate EXPLORE main records and EXPLORE_TOPIC records
	mainRecords := []TrendsRecord{}
	topicRecords := []TrendsRecord{}

	for _, record := range records {
		if record.DataType == "EXPLORE" {
			mainRecords = append(mainRecords, record)
		} else if record.DataType == "EXPLORE_TOPIC" {
			topicRecords = append(topicRecords, record)
		}
	}

	// If we have main EXPLORE records, use them for summary data
	if len(mainRecords) > 0 {
		// Use the first main record for summary data
		mainRecord := mainRecords[0]
		content.WriteString(fmt.Sprintf("**数据点**: %d | **平均兴趣度**: %d\n\n",
			mainRecord.DataPoints, mainRecord.AvgInterest))

		// Display hot topics from main record's raw data
		if mainRecord.RawData != nil && mainRecord.RawData.RelatedTopics != nil {
			if len(mainRecord.RawData.RelatedTopics.Top) > 0 {
				content.WriteString("**热门主题**:\n")
				for _, topic := range mainRecord.RawData.RelatedTopics.Top {
					content.WriteString(fmt.Sprintf("● %s (%d)\n",
						topic.Topic.Title, topic.ExtractedValue))
				}
				content.WriteString("\n")
			}
		}
	} else if len(topicRecords) > 0 {
		// If no main records, try to collect topics from EXPLORE_TOPIC records
		content.WriteString("**热门主题**:\n")
		for _, record := range topicRecords {
			if record.AvgInterest > 0 { // Only show topics with valid interest scores
				content.WriteString(fmt.Sprintf("● %s (%d)\n", record.Query, record.AvgInterest))
			}
		}
		content.WriteString("\n")
	}
}

// formatTrendingData formats TRENDING_NOW type data
func (d *DingTalkService) formatTrendingData(content *strings.Builder, records []TrendsRecord) {
	// Display up to 10 trending items, show what we have
	displayCount := len(records)
	if displayCount > 10 {
		displayCount = 10
	}

	for i := 0; i < displayCount; i++ {
		record := records[i]
		content.WriteString(fmt.Sprintf("● %s\n", record.Query))
	}
	content.WriteString("\n")
}

// formatRelatedQueriesData formats RELATED_QUERIES type data
func (d *DingTalkService) formatRelatedQueriesData(content *strings.Builder, records []TrendsRecord) {
	if len(records) == 0 {
		return
	}

	// Use the first record for summary data
	record := records[0]
	content.WriteString(fmt.Sprintf("**数据点**: %d | **平均兴趣度**: %d\n\n",
		record.DataPoints, record.AvgInterest))

	// Display related queries from raw data
	if record.RawData != nil && record.RawData.RelatedQueries != nil {
		// Display top queries
		if len(record.RawData.RelatedQueries.Top) > 0 {
			content.WriteString("**热门相关搜索**:\n")
			displayCount := len(record.RawData.RelatedQueries.Top)
			if displayCount > 10 {
				displayCount = 10
			}
			for i := 0; i < displayCount; i++ {
				query := record.RawData.RelatedQueries.Top[i]
				content.WriteString(fmt.Sprintf("● %s (%d)\n",
					query.Query, query.ExtractedValue))
			}
			content.WriteString("\n")
		}

		// Display rising queries if available
		if len(record.RawData.RelatedQueries.Rising) > 0 {
			content.WriteString("**上升相关搜索**:\n")
			displayCount := len(record.RawData.RelatedQueries.Rising)
			if displayCount > 5 {
				displayCount = 5
			}
			for i := 0; i < displayCount; i++ {
				query := record.RawData.RelatedQueries.Rising[i]
				content.WriteString(fmt.Sprintf("● %s (+%d%%)\n",
					query.Query, query.ExtractedValue))
			}
			content.WriteString("\n")
		}
	}
}

// formatRelatedTopicsData formats RELATED_TOPICS type data
func (d *DingTalkService) formatRelatedTopicsData(content *strings.Builder, records []TrendsRecord) {
	if len(records) == 0 {
		return
	}

	// Use the first record for summary data
	record := records[0]
	content.WriteString(fmt.Sprintf("**数据点**: %d | **平均兴趣度**: %d\n\n",
		record.DataPoints, record.AvgInterest))

	// Display related topics from raw data
	if record.RawData != nil && record.RawData.RelatedTopics != nil {
		// Display top topics
		if len(record.RawData.RelatedTopics.Top) > 0 {
			content.WriteString("**热门相关主题**:\n")
			displayCount := len(record.RawData.RelatedTopics.Top)
			if displayCount > 10 {
				displayCount = 10
			}
			for i := 0; i < displayCount; i++ {
				topic := record.RawData.RelatedTopics.Top[i]
				content.WriteString(fmt.Sprintf("● %s (%d)\n",
					topic.Topic.Title, topic.ExtractedValue))
			}
			content.WriteString("\n")
		}

		// Display rising topics if available
		if len(record.RawData.RelatedTopics.Rising) > 0 {
			content.WriteString("**上升相关主题**:\n")
			displayCount := len(record.RawData.RelatedTopics.Rising)
			if displayCount > 5 {
				displayCount = 5
			}
			for i := 0; i < displayCount; i++ {
				topic := record.RawData.RelatedTopics.Rising[i]
				content.WriteString(fmt.Sprintf("● %s (+%d%%)\n",
					topic.Topic.Title, topic.ExtractedValue))
			}
			content.WriteString("\n")
		}
	}
}

// SendMessage sends a message to specified recipients
func (d *DingTalkService) SendMessage(message *DingTalkMessage, recipientIDs []string) error {
	if !d.config.BotEnabled {
		log.Println("📱 DingTalk bot is disabled, skipping message send")
		return nil
	}

	log.Printf("📱 Sending DingTalk message: %s to %d recipients", message.Title, len(recipientIDs))
	log.Printf("📱 Recipients: %v", recipientIDs)

	// Get access token first
	accessToken, err := d.GetAccessToken()
	if err != nil {
		log.Printf("❌ Failed to get access token: %v", err)
		return fmt.Errorf("failed to get access token: %w", err)
	}
	log.Printf("📱 ✅ Access token obtained successfully")

	// Send to each recipient
	successCount := 0
	for i, recipientID := range recipientIDs {
		log.Printf("📱 [%d/%d] Sending to recipient: %s", i+1, len(recipientIDs), recipientID)
		err := d.sendMessageToRecipient(accessToken, message, recipientID)
		if err != nil {
			log.Printf("❌ [%d/%d] Failed to send message to %s: %v", i+1, len(recipientIDs), recipientID, err)
		} else {
			log.Printf("✅ [%d/%d] Message sent successfully to: %s", i+1, len(recipientIDs), recipientID)
			successCount++
		}
	}

	log.Printf("📱 Message sending completed: %d/%d successful", successCount, len(recipientIDs))

	if successCount == 0 {
		return fmt.Errorf("failed to send message to any recipient")
	}

	return nil
}

// SendToAllRecipients sends a message to all enabled recipients
func (d *DingTalkService) SendToAllRecipients(message *DingTalkMessage) error {
	recipients, err := d.GetRecipientsFromConfig()
	if err != nil {
		return fmt.Errorf("failed to get recipients: %w", err)
	}

	if len(recipients) == 0 {
		log.Println("📱 No recipients found for DingTalk message")
		return nil
	}

	recipientIDs := make([]string, len(recipients))
	for i, recipient := range recipients {
		recipientIDs[i] = recipient.RecipientID
	}

	return d.SendMessage(message, recipientIDs)
}

// MarkRecordsAsPushed marks trends records as pushed
func (d *DingTalkService) MarkRecordsAsPushed(recordIDs []int) error {
	if d.database == nil {
		return fmt.Errorf("database not available")
	}

	if len(recordIDs) == 0 {
		return nil
	}

	// Build placeholders for IN clause
	placeholders := make([]string, len(recordIDs))
	args := make([]interface{}, len(recordIDs))
	for i, id := range recordIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	query := fmt.Sprintf(`
		UPDATE trends_data 
		SET push_status = 1, pushed_at = CURRENT_TIMESTAMP 
		WHERE id IN (%s)
	`, strings.Join(placeholders, ","))

	_, err := d.database.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to mark records as pushed: %w", err)
	}

	log.Printf("📱 Marked %d records as pushed", len(recordIDs))
	return nil
}

// GetUnpushedRecords returns records that haven't been pushed yet
func (d *DingTalkService) GetUnpushedRecords(hours int) ([]TrendsRecord, error) {
	if d.database == nil {
		return nil, fmt.Errorf("database not available")
	}

	query := `
		SELECT id, task_group, query, geo, date_range, data_type, 
			   executed_at, avg_interest, data_points, peak_date, raw_data
		FROM trends_data 
		WHERE push_status = 0 
		AND executed_at >= datetime('now', '-' || ? || ' hours')
		ORDER BY executed_at DESC
	`

	rows, err := d.database.db.Query(query, hours)
	if err != nil {
		return nil, fmt.Errorf("failed to query unpushed records: %w", err)
	}
	defer rows.Close()

	var records []TrendsRecord
	for rows.Next() {
		var record TrendsRecord
		var rawDataJSON string

		err := rows.Scan(&record.ID, &record.TaskGroup, &record.Query, &record.Geo,
			&record.DateRange, &record.DataType, &record.ExecutedAt,
			&record.AvgInterest, &record.DataPoints, &record.PeakDate, &rawDataJSON)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trends record: %w", err)
		}

		// Deserialize raw data
		var rawData models.TrendsResponse
		if err := json.Unmarshal([]byte(rawDataJSON), &rawData); err != nil {
			log.Printf("Warning: failed to deserialize raw data for record %d: %v", record.ID, err)
		} else {
			record.RawData = &rawData
		}

		records = append(records, record)
	}

	return records, nil
}
