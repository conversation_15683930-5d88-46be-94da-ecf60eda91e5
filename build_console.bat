@echo off
echo Building Go LilyBot API Trends with Windows Keep-Alive...

set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=1

if exist "bin\go_lilybot_api_trends.exe" (
    echo Cleaning old build...
    del "bin\go_lilybot_api_trends.exe"
)

if not exist "bin" mkdir bin

echo Compiling...
go build -ldflags "-s -w" -o bin\go_lilybot_api_trends.exe cmd\server\main.go

if exist "bin\go_lilybot_api_trends.exe" (
    echo Build successful!
    echo Executable created: bin\go_lilybot_api_trends.exe

    echo Copying configuration files...
    copy ".env.example" "bin\.env.example" >nul 2>&1
    copy ".env.dingtalk" "bin\.env.dingtalk" >nul 2>&1
    copy ".env.gemini" "bin\.env.gemini" >nul 2>&1

    if not exist "bin\data" mkdir "bin\data"

    echo Build complete!
    echo Features added:
    echo   - Windows system keep-alive
    echo   - Process priority management
    echo   - Network heartbeat monitoring
    echo   - System sleep prevention
    echo.
    echo Next steps:
    echo   1. Configure .env files in bin\ directory
    echo   2. Run: bin\go_lilybot_api_trends.exe
    echo   3. Check admin interface for keep-alive status
) else (
    echo Build failed!
    echo Please check for compilation errors above.
    exit /b 1
)

pause
