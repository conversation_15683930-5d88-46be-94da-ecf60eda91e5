package services

import (
	"fmt"
	"log"
	"net/url"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/config"
	"go_lilybot_api_trends/internal/models"

	"github.com/robfig/cron/v3"
)

// SchedulerService handles scheduled tasks
type SchedulerService struct {
	cron            *cron.Cron
	serpApiService  *SerpApiService
	config          *config.Config
	database        *DatabaseService
	dingTalkService *DingTalkService
	results         map[string]*models.TrendsResponse // Keep for backward compatibility
}

// NewSchedulerService creates a new scheduler service
func NewSchedulerService(serpApiService *SerpApiService, cfg *config.Config, database *DatabaseService, dingTalkService *DingTalkService) *SchedulerService {
	// Create cron with timezone support
	location, err := time.LoadLocation(cfg.Scheduler.Timezone)
	if err != nil {
		log.Printf("Warning: Failed to load timezone %s, using UTC: %v", cfg.Scheduler.Timezone, err)
		location = time.UTC
	}

	return &SchedulerService{
		cron:            cron.New(cron.WithLocation(location)),
		serpApiService:  serpApiService,
		config:          cfg,
		database:        database,
		dingTalkService: dingTalkService,
		results:         make(map[string]*models.TrendsResponse),
	}
}

// Start starts the scheduler
func (s *SchedulerService) Start() error {
	if !s.config.Scheduler.Enabled {
		log.Println("📅 Scheduler is disabled")
		return nil
	}

	// Parse time format (HH:MM) to cron format
	cronExpr, err := s.parseToCronExpression(s.config.Scheduler.Time)
	if err != nil {
		return fmt.Errorf("failed to parse scheduler time: %w", err)
	}

	// Add scheduled job
	_, err = s.cron.AddFunc(cronExpr, s.executeScheduledTasks)
	if err != nil {
		return fmt.Errorf("failed to add scheduled job: %w", err)
	}

	s.cron.Start()
	log.Printf("📅 Scheduler started - will run at %s (%s)", s.config.Scheduler.Time, s.config.Scheduler.Timezone)
	log.Printf("📅 Next run: %s", s.getNextRunTime())

	return nil
}

// Stop stops the scheduler
func (s *SchedulerService) Stop() {
	if s.cron != nil {
		s.cron.Stop()
		log.Println("📅 Scheduler stopped")
	}
}

// executeScheduledTasks executes all enabled task groups
func (s *SchedulerService) executeScheduledTasks() {
	log.Println("🚀 Starting scheduled task execution...")
	startTime := time.Now()
	currentTime := time.Now()

	if !s.config.TaskGroupsEnabled {
		log.Println("⚠️  Task groups are disabled")
		return
	}

	// Check global workdays setting
	if s.config.Scheduler.WorkdaysOnly {
		if !s.isWorkday(currentTime, s.config.Scheduler.WorkdayPattern) {
			log.Printf("⏭️  Skipping execution - today is not a workday (pattern: %s, day: %s)",
				s.config.Scheduler.WorkdayPattern, currentTime.Weekday().String())
			return
		}
		log.Printf("✅ Workday check passed (pattern: %s, day: %s)",
			s.config.Scheduler.WorkdayPattern, currentTime.Weekday().String())
	}

	totalQueries := 0
	for _, taskGroup := range s.config.TaskGroups {
		if !taskGroup.Enabled {
			log.Printf("⏭️  Skipping disabled task group: %s", taskGroup.Name)
			continue
		}

		// Check individual task group workdays setting
		if taskGroup.WorkdaysOnly {
			pattern := taskGroup.WorkdayPattern
			if pattern == "" {
				pattern = "MON-FRI" // Default pattern
			}
			if !s.isWorkday(currentTime, pattern) {
				log.Printf("⏭️  Skipping task group '%s' - today is not a workday (pattern: %s, day: %s)",
					taskGroup.Name, pattern, currentTime.Weekday().String())
				continue
			}
			log.Printf("✅ Task group '%s' workday check passed (pattern: %s)", taskGroup.Name, pattern)
		}

		log.Printf("📊 Executing task group: %s", taskGroup.Name)
		queriesProcessed := s.executeTaskGroup(taskGroup)
		totalQueries += queriesProcessed

		// Log execution to database
		if s.database != nil {
			duration := time.Since(startTime)
			err := s.database.LogExecution(taskGroup.Name, "completed",
				fmt.Sprintf("Processed %d queries", queriesProcessed), duration, queriesProcessed)
			if err != nil {
				log.Printf("⚠️  Failed to log execution: %v", err)
			}
		}
	}

	duration := time.Since(startTime)
	log.Printf("✅ Scheduled task execution completed in %v (processed %d queries)", duration, totalQueries)

	// Auto-push to DingTalk if enabled and tasks were executed
	if totalQueries > 0 && s.dingTalkService != nil {
		s.autoPushToDingTalk()
	}
}

// executeTaskGroup executes a single task group
func (s *SchedulerService) executeTaskGroup(taskGroup config.TaskGroup) int {
	processedCount := 0

	// Check if this is a URL-style task configuration
	if taskGroup.OriginalURL != "" {
		return s.executeURLStyleTask(taskGroup)
	}

	// Handle explore tasks (no specific queries needed - like Google Trends explore page)
	if taskGroup.DataType == "EXPLORE" {
		log.Printf("🔍 Processing explore mode for geo: %s", taskGroup.Geo)

		// Generate batch ID for this execution
		batchID := fmt.Sprintf("%s_explore_%s", taskGroup.Name, time.Now().Format("2006-01-02_15-04-05"))

		// Get explore data (like the explore page without specific keywords)
		category := taskGroup.Category
		if category == "" {
			category = "0" // Default to "All categories"
		}
		exploreDataType := taskGroup.ExploreDataType
		if exploreDataType == "" {
			exploreDataType = "RELATED_TOPICS" // Default explore data type
		}
		exploreResult, err := s.serpApiService.GetTrendsExplore(
			taskGroup.Geo,
			taskGroup.Language,
			taskGroup.Timezone,
			taskGroup.Region,
			category,
			exploreDataType,
			taskGroup.Date,
		)
		if err != nil {
			log.Printf("❌ Failed to get explore data: %v", err)
			return 0
		}

		// Store result in memory
		s.results[batchID] = exploreResult

		// Save main explore task to database
		if s.database != nil {
			err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, "explore", taskGroup.Geo,
				taskGroup.Date, taskGroup.DataType, exploreResult, batchID, "")
			if err != nil {
				log.Printf("⚠️  Failed to save explore data to database: %v", err)
			}

			// Save individual related topics as sub-records
			if exploreResult.RelatedTopics != nil {
				// Save top topics
				for _, topic := range exploreResult.RelatedTopics.Top {
					if topic.Topic.Title != "" {
						// Create a simplified response for each topic
						topicResponse := &models.TrendsResponse{
							SearchMetadata:   exploreResult.SearchMetadata,
							SearchParameters: exploreResult.SearchParameters,
							RelatedTopics: &models.RelatedTopics{
								Top: []models.TopicItem{topic},
							},
						}
						err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, topic.Topic.Title, taskGroup.Geo,
							taskGroup.Date, "EXPLORE_TOPIC", topicResponse, batchID, batchID)
						if err != nil {
							log.Printf("⚠️  Failed to save topic data: %v", err)
						}
					}
				}

				// Save rising topics
				for _, topic := range exploreResult.RelatedTopics.Rising {
					if topic.Topic.Title != "" {
						// Create a simplified response for each topic
						topicResponse := &models.TrendsResponse{
							SearchMetadata:   exploreResult.SearchMetadata,
							SearchParameters: exploreResult.SearchParameters,
							RelatedTopics: &models.RelatedTopics{
								Rising: []models.TopicItem{topic},
							},
						}
						err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, topic.Topic.Title+" (Rising)", taskGroup.Geo,
							taskGroup.Date, "EXPLORE_TOPIC", topicResponse, batchID, batchID)
						if err != nil {
							log.Printf("⚠️  Failed to save rising topic data: %v", err)
						}
					}
				}
			}
		}

		log.Printf("✅ Successfully processed explore data with %d related topics",
			len(exploreResult.RelatedTopics.Top)+len(exploreResult.RelatedTopics.Rising))
		return 1
	}

	// Handle trending now tasks (no specific queries needed)
	if taskGroup.DataType == "TRENDING_NOW" {
		log.Printf("🔥 Processing trending now for geo: %s", taskGroup.Geo)

		// Generate batch ID for this execution
		batchID := fmt.Sprintf("%s_trending_%s", taskGroup.Name, time.Now().Format("2006-01-02_15-04-05"))

		// Get trending searches
		hours := taskGroup.Hours
		if hours == 0 {
			hours = 168 // Default to 7 days
		}
		// Use CategoryID directly - if empty, SerpApi will return all categories
		categoryID := taskGroup.CategoryID
		trendingResult, err := s.serpApiService.GetTrendingNow(taskGroup.Geo, hours, categoryID, false)
		if err != nil {
			log.Printf("❌ Failed to get trending now data: %v", err)
			return 0
		}

		// Convert TrendingNowResponse to TrendsResponse for storage
		mainTrendsResp := &models.TrendsResponse{
			SearchMetadata: trendingResult.SearchMetadata,
			SearchParameters: models.SearchParameters{
				Engine:   "google_trends_trending_now",
				Query:    "trending_now",
				Geo:      taskGroup.Geo,
				DataType: taskGroup.DataType,
			},
		}

		// Store main result in memory
		s.results[batchID] = mainTrendsResp

		// Save main trending task to database
		if s.database != nil {
			err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, "trending_now", taskGroup.Geo,
				"now 7-d", taskGroup.DataType, mainTrendsResp, batchID, "")
			if err != nil {
				log.Printf("⚠️  Failed to save trending now data to database: %v", err)
			}
		}

		processedCount := 0

		// Process each trending search as sub-record
		for _, trending := range trendingResult.TrendingSearches {
			if !trending.Active {
				continue // Skip inactive trends
			}

			log.Printf("🔥 Found trending: %s (volume: %d, increase: %d%%)",
				trending.Query, trending.SearchVolume, trending.IncreasePercentage)

			// Convert trending search to TrendsResponse format for storage
			trendsResp := &models.TrendsResponse{
				SearchMetadata: trendingResult.SearchMetadata,
				SearchParameters: models.SearchParameters{
					Engine:   "google_trends_trending_now",
					Query:    trending.Query,
					Geo:      taskGroup.Geo,
					DataType: "TRENDING_NOW",
				},
			}

			// Store individual result in memory
			key := fmt.Sprintf("%s_%s_%s", taskGroup.Name, trending.Query, time.Now().Format("2006-01-02_15-04-05"))
			s.results[key] = trendsResp

			// Save to database as sub-record
			if s.database != nil {
				err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, trending.Query, taskGroup.Geo,
					"now 7-d", "TRENDING_ITEM", trendsResp, batchID, batchID)
				if err != nil {
					log.Printf("⚠️  Failed to save trending search to database: %v", err)
				}
			}

			processedCount++
		}

		log.Printf("✅ Successfully processed %d trending searches under batch %s", processedCount, batchID)
		return processedCount
	}

	// Handle regular query-based tasks
	for _, query := range taskGroup.Queries {
		log.Printf("🔍 Processing query: %s (Geo: %s, Date: %s)", query, taskGroup.Geo, taskGroup.Date)

		req := &models.TrendsRequest{
			Query:    query,
			DataType: taskGroup.DataType,
			Geo:      taskGroup.Geo,
			Date:     taskGroup.Date,
		}

		result, err := s.serpApiService.GetTrends(req)
		if err != nil {
			log.Printf("❌ Failed to get trends for query '%s': %v", query, err)
			continue
		}

		// Store result in memory (for backward compatibility)
		key := fmt.Sprintf("%s_%s_%s", taskGroup.Name, query, time.Now().Format("2006-01-02_15-04-05"))
		s.results[key] = result

		// Save to database
		if s.database != nil {
			err = s.database.SaveTrendsData(taskGroup.Name, query, taskGroup.Geo,
				taskGroup.Date, taskGroup.DataType, result)
			if err != nil {
				log.Printf("⚠️  Failed to save to database: %v", err)
			}
		}

		log.Printf("✅ Successfully processed query: %s", query)
		processedCount++

		// Add delay between requests to avoid rate limiting
		time.Sleep(2 * time.Second)
	}

	return processedCount
}

// GetResults returns all stored results
func (s *SchedulerService) GetResults() map[string]*models.TrendsResponse {
	return s.results
}

// GetLatestResults returns the latest results for each query
func (s *SchedulerService) GetLatestResults() map[string]*models.TrendsResponse {
	// Always get from database to ensure data consistency
	if s.database != nil {
		records, err := s.database.GetLatestTrendsData(50)
		if err != nil {
			log.Printf("⚠️  Failed to get data from database: %v", err)
			// Return empty map instead of falling back to potentially stale memory cache
			return make(map[string]*models.TrendsResponse)
		}

		latest := make(map[string]*models.TrendsResponse)
		for _, record := range records {
			key := fmt.Sprintf("%s_%s", record.TaskGroup, record.Query)
			if record.RawData != nil {
				latest[key] = record.RawData
			}
		}
		return latest
	}

	// If no database available, return empty map
	return make(map[string]*models.TrendsResponse)
}

// parseToCronExpression converts HH:MM format to cron expression
func (s *SchedulerService) parseToCronExpression(timeStr string) (string, error) {
	t, err := time.Parse("15:04", timeStr)
	if err != nil {
		return "", fmt.Errorf("invalid time format, expected HH:MM: %w", err)
	}

	// Cron format: minute hour * * *
	return fmt.Sprintf("%d %d * * *", t.Minute(), t.Hour()), nil
}

// getNextRunTime returns the next scheduled run time
func (s *SchedulerService) getNextRunTime() string {
	entries := s.cron.Entries()
	if len(entries) > 0 {
		return entries[0].Next.Format("2006-01-02 15:04:05 MST")
	}
	return "No scheduled runs"
}

// ExecuteTaskGroupManually executes a task group manually (for testing/admin interface)
func (s *SchedulerService) ExecuteTaskGroupManually(taskGroupName string) error {
	startTime := time.Now()

	for _, taskGroup := range s.config.TaskGroups {
		if taskGroup.Name == taskGroupName && taskGroup.Enabled {
			log.Printf("🔧 Manually executing task group: %s", taskGroupName)
			queriesProcessed := s.executeTaskGroup(taskGroup)

			// Log execution to database
			if s.database != nil {
				duration := time.Since(startTime)
				err := s.database.LogExecution(taskGroup.Name, "manual_completed",
					fmt.Sprintf("Manually processed %d queries", queriesProcessed), duration, queriesProcessed)
				if err != nil {
					log.Printf("⚠️  Failed to log manual execution: %v", err)
				}
			}

			return nil
		}
	}
	return fmt.Errorf("task group '%s' not found or disabled", taskGroupName)
}

// isWorkday checks if the given time falls on a workday according to the pattern
func (s *SchedulerService) isWorkday(t time.Time, pattern string) bool {
	weekday := t.Weekday()

	switch pattern {
	case "MON-FRI":
		// Monday to Friday
		return weekday >= time.Monday && weekday <= time.Friday
	case "MON-SAT":
		// Monday to Saturday
		return weekday >= time.Monday && weekday <= time.Saturday
	case "DAILY":
		// Every day
		return true
	case "MON-THU":
		// Monday to Thursday
		return weekday >= time.Monday && weekday <= time.Thursday
	case "TUE-FRI":
		// Tuesday to Friday
		return weekday >= time.Tuesday && weekday <= time.Friday
	default:
		// Custom pattern: parse comma-separated weekdays
		return s.parseCustomWorkdayPattern(weekday, pattern)
	}
}

// parseCustomWorkdayPattern parses custom workday patterns like "MON,WED,FRI"
func (s *SchedulerService) parseCustomWorkdayPattern(weekday time.Weekday, pattern string) bool {
	if pattern == "" {
		return false
	}

	// Map weekday names to time.Weekday
	weekdayMap := map[string]time.Weekday{
		"SUN": time.Sunday,
		"MON": time.Monday,
		"TUE": time.Tuesday,
		"WED": time.Wednesday,
		"THU": time.Thursday,
		"FRI": time.Friday,
		"SAT": time.Saturday,
	}

	// Split pattern by comma and check each day
	days := strings.Split(strings.ToUpper(pattern), ",")
	for _, day := range days {
		day = strings.TrimSpace(day)
		if mappedDay, exists := weekdayMap[day]; exists {
			if weekday == mappedDay {
				return true
			}
		}
	}

	return false
}

// GetWorkdayInfo returns information about workday settings
func (s *SchedulerService) GetWorkdayInfo() map[string]interface{} {
	info := map[string]interface{}{
		"global_workdays_only":   s.config.Scheduler.WorkdaysOnly,
		"global_workday_pattern": s.config.Scheduler.WorkdayPattern,
		"current_day":            time.Now().Weekday().String(),
		"is_global_workday":      true,
	}

	if s.config.Scheduler.WorkdaysOnly {
		info["is_global_workday"] = s.isWorkday(time.Now(), s.config.Scheduler.WorkdayPattern)
	}

	// Add task group workday info
	taskGroupInfo := make([]map[string]interface{}, 0)
	for _, taskGroup := range s.config.TaskGroups {
		groupInfo := map[string]interface{}{
			"name":            taskGroup.Name,
			"workdays_only":   taskGroup.WorkdaysOnly,
			"workday_pattern": taskGroup.WorkdayPattern,
			"is_workday":      true,
		}

		if taskGroup.WorkdaysOnly {
			pattern := taskGroup.WorkdayPattern
			if pattern == "" {
				pattern = "MON-FRI"
			}
			groupInfo["is_workday"] = s.isWorkday(time.Now(), pattern)
			groupInfo["effective_pattern"] = pattern
		}

		taskGroupInfo = append(taskGroupInfo, groupInfo)
	}

	info["task_groups"] = taskGroupInfo
	return info
}

// autoPushToDingTalk automatically pushes latest trends data to all configured DingTalk recipients
func (s *SchedulerService) autoPushToDingTalk() {
	log.Println("📱 Starting automatic DingTalk push after scheduled task execution...")

	if s.database == nil {
		log.Println("⚠️  Database not available, skipping auto-push")
		return
	}

	// Get all latest records from all task groups
	var allRecordIDs []int

	for _, taskGroup := range s.config.TaskGroups {
		if !taskGroup.Enabled {
			continue
		}

		// Get latest records for this task group (within last 10 minutes)
		records, err := s.database.GetTrendsDataWithFilters(taskGroup.Name, "", "", 1) // 1 day to get recent data
		if err != nil {
			log.Printf("⚠️  Failed to get records for task group %s: %v", taskGroup.Name, err)
			continue
		}

		// Filter to get only the most recent records (within 10 minutes)
		now := time.Now()
		for _, record := range records {
			if record.ExecutedAt.After(now.Add(-10 * time.Minute)) {
				allRecordIDs = append(allRecordIDs, record.ID)
			}
		}
	}

	if len(allRecordIDs) == 0 {
		log.Println("⚠️  No recent records found for auto-push")
		return
	}

	log.Printf("📱 Found %d recent records for auto-push", len(allRecordIDs))

	// Get records and format message
	records, err := s.database.GetTrendsRecordsByIDs(allRecordIDs)
	if err != nil {
		log.Printf("❌ Failed to get records for auto-push: %v", err)
		return
	}

	// Format message
	message := s.dingTalkService.FormatTrendsMessage("Scheduled Tasks", records)

	// Push to all configured recipients
	err = s.dingTalkService.SendToAllRecipients(message)
	if err != nil {
		log.Printf("❌ Auto-push to DingTalk failed: %v", err)
	} else {
		log.Printf("✅ Auto-push to DingTalk completed successfully")

		// Mark records as pushed
		err = s.dingTalkService.MarkRecordsAsPushed(allRecordIDs)
		if err != nil {
			log.Printf("⚠️  Failed to mark records as pushed: %v", err)
		}
	}
}

// executeURLStyleTask executes a task group using URL-style configuration
func (s *SchedulerService) executeURLStyleTask(taskGroup config.TaskGroup) int {
	log.Printf("🌐 Executing URL-style task: %s", taskGroup.Name)
	log.Printf("🔗 URL: %s", taskGroup.OriginalURL)

	// Generate batch ID for this execution
	batchID := fmt.Sprintf("%s_%s", taskGroup.Name, time.Now().Format("2006-01-02_15-04-05"))

	// Execute the direct URL request
	result, err := s.serpApiService.ExecuteDirectURL(taskGroup.OriginalURL)
	if err != nil {
		log.Printf("❌ Failed to execute URL-style task '%s': %v", taskGroup.Name, err)
		return 0
	}

	// Determine the result type and save accordingly
	switch typedResult := result.(type) {
	case *models.TrendsResponse:
		// Handle trends response
		log.Printf("✅ Got trends response for task: %s", taskGroup.Name)

		// Save to database
		if s.database != nil {
			// Determine query from URL or use task name
			query := s.extractQueryFromURL(taskGroup.OriginalURL)
			if query == "" {
				query = taskGroup.Name
			}

			err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, query, taskGroup.Geo,
				taskGroup.Date, taskGroup.DataType, typedResult, batchID, "")
			if err != nil {
				log.Printf("⚠️  Failed to save trends data to database: %v", err)
			}
		}

	case *models.TrendingNowResponse:
		// Handle trending now response
		log.Printf("✅ Got trending now response for task: %s", taskGroup.Name)

		// Convert TrendingNowResponse to TrendsResponse for storage
		mainTrendsResp := &models.TrendsResponse{
			SearchMetadata: typedResult.SearchMetadata,
			SearchParameters: models.SearchParameters{
				Engine:   "google_trends_trending_now",
				Query:    "trending_now",
				Geo:      taskGroup.Geo,
				DataType: taskGroup.DataType,
			},
		}

		// Save main trending task to database
		if s.database != nil {
			err = s.database.SaveTrendsDataWithBatch(taskGroup.Name, "trending_now", taskGroup.Geo,
				"now 7-d", taskGroup.DataType, mainTrendsResp, batchID, "")
			if err != nil {
				log.Printf("⚠️  Failed to save trending now data to database: %v", err)
			}
		}

	default:
		log.Printf("⚠️  Unknown response type for task: %s", taskGroup.Name)
		return 0
	}

	log.Printf("✅ Successfully processed URL-style task: %s", taskGroup.Name)
	return 1
}

// extractQueryFromURL extracts the 'q' parameter from a URL
func (s *SchedulerService) extractQueryFromURL(urlString string) string {
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return ""
	}

	return parsedURL.Query().Get("q")
}
