package config

import (
	"time"
)

// KeepAliveConfig holds configuration for Windows keep-alive functionality
type KeepAliveConfig struct {
	// Basic settings
	Enabled bool `json:"enabled"`
	
	// Heartbeat settings
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	NetworkHeartbeat  bool          `json:"network_heartbeat"`
	HeartbeatURL      string        `json:"heartbeat_url"`
	
	// Process priority settings
	ProcessPriority string `json:"process_priority"` // "normal", "high", "realtime"
	
	// System settings
	PreventSleep     bool `json:"prevent_sleep"`
	PreventHibernate bool `json:"prevent_hibernate"`
	
	// Monitoring settings
	LogInterval      time.Duration `json:"log_interval"`
	MonitorResources bool          `json:"monitor_resources"`
}

// LoadKeepAliveConfig loads keep-alive configuration from environment variables
func LoadKeepAliveConfig() *KeepAliveConfig {
	return &KeepAliveConfig{
		Enabled:           getEnvAsBool("KEEPALIVE_ENABLED", true),
		HeartbeatInterval: getEnvAsDuration("KEEPALIVE_HEARTBEAT_INTERVAL", 5*time.Minute),
		NetworkHeartbeat:  getEnvAsBool("KEEPALIVE_NETWORK_HEARTBEAT", true),
		HeartbeatURL:      getEnv("KEEPALIVE_HEARTBEAT_URL", "https://www.google.com/generate_204"),
		ProcessPriority:   getEnv("KEEPALIVE_PROCESS_PRIORITY", "high"),
		PreventSleep:      getEnvAsBool("KEEPALIVE_PREVENT_SLEEP", true),
		PreventHibernate:  getEnvAsBool("KEEPALIVE_PREVENT_HIBERNATE", true),
		LogInterval:       getEnvAsDuration("KEEPALIVE_LOG_INTERVAL", 10*time.Minute),
		MonitorResources:  getEnvAsBool("KEEPALIVE_MONITOR_RESOURCES", true),
	}
}

// getEnvAsDuration parses environment variable as duration with fallback
func getEnvAsDuration(key string, fallback time.Duration) time.Duration {
	value := getEnv(key, "")
	if value == "" {
		return fallback
	}
	
	duration, err := time.ParseDuration(value)
	if err != nil {
		return fallback
	}
	
	return duration
}
