@echo off
echo 🚀 Building Go LilyBot API Trends with Windows Keep-Alive...

REM 设置环境变量
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=1

REM 清理旧的构建文件
if exist "bin\go_lilybot_api_trends.exe" (
    echo 🧹 Cleaning old build...
    del "bin\go_lilybot_api_trends.exe"
)

REM 创建bin目录（如果不存在）
if not exist "bin" mkdir bin

REM 编译程序
echo 🔨 Compiling...
go build -ldflags "-s -w -H windowsgui" -o bin\go_lilybot_api_trends.exe cmd\server\main.go

REM 检查编译结果
if exist "bin\go_lilybot_api_trends.exe" (
    echo ✅ Build successful!
    echo 📦 Executable created: bin\go_lilybot_api_trends.exe
    
    REM 复制配置文件到bin目录
    echo 📋 Copying configuration files...
    copy ".env.example" "bin\.env.example" >nul 2>&1
    copy ".env.dingtalk" "bin\.env.dingtalk" >nul 2>&1
    copy ".env.gemini" "bin\.env.gemini" >nul 2>&1
    
    REM 创建data目录
    if not exist "bin\data" mkdir "bin\data"
    
    echo 🎉 Build complete! 
    echo 💡 Features added:
    echo    - Windows system keep-alive (防止效能模式)
    echo    - Process priority management
    echo    - Network heartbeat monitoring
    echo    - System sleep prevention
    echo.
    echo 📝 Next steps:
    echo    1. Configure .env files in bin\ directory
    echo    2. Run: bin\go_lilybot_api_trends.exe
    echo    3. Check admin interface for keep-alive status
) else (
    echo ❌ Build failed!
    echo Please check for compilation errors above.
    exit /b 1
)

pause
