package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go_lilybot_api_trends/internal/config"
)

// GeminiService handles Gemini AI interactions
type GeminiService struct {
	config *config.GeminiConfig
	client *http.Client
}

// GeminiRequest represents a request to Gemini API
type GeminiRequest struct {
	Contents []GeminiContent `json:"contents"`
}

// GeminiContent represents content in Gemini request
type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
}

// GeminiPart represents a part of content
type GeminiPart struct {
	Text string `json:"text"`
}

// GeminiResponse represents response from Gemini API
type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
}

// GeminiCandidate represents a candidate response
type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

// NewGeminiService creates a new Gemini service
func NewGeminiService() (*GeminiService, error) {
	geminiConfig, err := config.LoadGeminiConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load Gemini config: %w", err)
	}

	// Create HTTP client with optional proxy
	client := &http.Client{
		Timeout: time.Duration(geminiConfig.TimeoutSeconds) * time.Second,
	}

	// Configure proxy if enabled
	if geminiConfig.ProxyEnabled && geminiConfig.ProxyURL != "" {
		proxyURL, err := url.Parse(geminiConfig.ProxyURL)
		if err != nil {
			log.Printf("⚠️  Invalid proxy URL: %v", err)
		} else {
			client.Transport = &http.Transport{
				Proxy: http.ProxyURL(proxyURL),
			}
			log.Printf("🌐 Gemini client configured with proxy: %s", geminiConfig.ProxyURL)
		}
	}

	return &GeminiService{
		config: geminiConfig,
		client: client,
	}, nil
}

// AnalyzeTrendsData analyzes trends data and provides insights
func (g *GeminiService) AnalyzeTrendsData(trendsContent string) (string, error) {
	if !g.config.Enabled || !g.config.AnalysisEnabled {
		log.Println("🤖 Gemini analysis is disabled")
		return "", nil
	}

	if g.config.APIKey == "" {
		log.Println("⚠️  Gemini API key not configured")
		return "", nil
	}

	log.Println("🤖 Starting Gemini analysis...")

	// Create analysis prompt
	prompt := g.createAnalysisPrompt(trendsContent)

	// Call Gemini API with retry logic
	var response string
	var err error

	for i := 0; i < g.config.RetryCount; i++ {
		response, err = g.callGeminiAPI(prompt)
		if err == nil {
			log.Println("✅ Gemini analysis completed")
			return response, nil
		}

		log.Printf("⚠️  Gemini API attempt %d/%d failed: %v", i+1, g.config.RetryCount, err)

		// Check if it's a location restriction error
		if strings.Contains(err.Error(), "User location is not supported") {
			log.Println("🌍 Gemini API不支持当前地理位置，返回备用分析")
			return g.getFallbackAnalysis(trendsContent), nil
		}
	}

	log.Printf("❌ Gemini API call failed after %d attempts: %v", g.config.RetryCount, err)

	// Return fallback analysis instead of error
	log.Println("🔄 使用备用分析替代Gemini")
	return g.getFallbackAnalysis(trendsContent), nil
}

// createAnalysisPrompt creates a prompt for analyzing trends data
func (g *GeminiService) createAnalysisPrompt(trendsContent string) string {
	// Use configurable prompt template
	prompt := g.config.PromptTemplate

	// Replace placeholders with actual values
	prompt = strings.ReplaceAll(prompt, "{TRENDS_DATA}", trendsContent)
	prompt = strings.ReplaceAll(prompt, "{MAX_WORDS}", fmt.Sprintf("%d", g.config.MaxWords))
	prompt = strings.ReplaceAll(prompt, "{LANGUAGE}", g.config.Language)
	prompt = strings.ReplaceAll(prompt, "{ANALYSIS_POINTS}", fmt.Sprintf("%d", g.config.AnalysisPoints))

	return prompt
}

// callGeminiAPI calls the Gemini API
func (g *GeminiService) callGeminiAPI(prompt string) (string, error) {
	url := fmt.Sprintf("https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s",
		g.config.Model, g.config.APIKey)

	// Create request payload
	request := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{
					{Text: prompt},
				},
			},
		},
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := g.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var geminiResp GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no content in response")
	}

	analysis := geminiResp.Candidates[0].Content.Parts[0].Text
	return strings.TrimSpace(analysis), nil
}

// getFallbackAnalysis provides a simple fallback analysis when Gemini API is not available
func (g *GeminiService) getFallbackAnalysis(trendsContent string) string {
	// Simple rule-based analysis as fallback
	analysis := "📈 趋势洞察：\n"

	// Count regions and data types
	regions := []string{"美国", "中国", "香港", "台湾", "日本", "韩国"}

	regionCount := 0
	exploreCount := 0
	trendingCount := 0

	for _, region := range regions {
		if strings.Contains(trendsContent, region) {
			regionCount++
		}
	}

	if strings.Contains(trendsContent, "探索热门") {
		exploreCount++
	}
	if strings.Contains(trendsContent, "实时热门") {
		trendingCount++
	}

	// Generate insights based on content
	if regionCount > 1 {
		analysis += fmt.Sprintf("1. 本次数据覆盖%d个地区，展现了全球化的搜索趋势\n", regionCount)
	}

	if exploreCount > 0 && trendingCount > 0 {
		analysis += "2. 数据包含探索和实时热门两种类型，反映了用户的深度兴趣和即时关注\n"
	}

	// Check for common themes
	if strings.Contains(trendsContent, "Google") || strings.Contains(trendsContent, "谷歌") {
		analysis += "3. Google相关搜索热度较高，显示了用户对搜索工具的依赖\n"
	}

	if strings.Contains(trendsContent, "AI") || strings.Contains(trendsContent, "人工智能") || strings.Contains(trendsContent, "ChatGPT") {
		analysis += "3. AI相关话题持续受关注，反映了技术发展趋势\n"
	}

	analysis += "\n💡 注：当前使用备用分析，建议关注数据中的高频关键词和跨地区对比。"

	return analysis
}
