package models

import "time"

// TrendsResponse represents the main response from SerpApi Google Trends
type TrendsResponse struct {
	SearchMetadata    SearchMetadata    `json:"search_metadata"`
	SearchParameters  SearchParameters  `json:"search_parameters"`
	InterestOverTime  *InterestOverTime `json:"interest_over_time,omitempty"`
	InterestByRegion  []RegionInterest  `json:"interest_by_region,omitempty"`
	RelatedTopics     *RelatedTopics    `json:"related_topics,omitempty"`
	RelatedQueries    *RelatedQueries   `json:"related_queries,omitempty"`
	ComparedBreakdown []ComparedRegion  `json:"compared_breakdown_by_region,omitempty"`
}

// SearchMetadata contains metadata about the search
type SearchMetadata struct {
	ID              string  `json:"id"`
	Status          string  `json:"status"`
	JSONEndpoint    string  `json:"json_endpoint"`
	CreatedAt       string  `json:"created_at"`
	ProcessedAt     string  `json:"processed_at"`
	GoogleTrendsURL string  `json:"google_trends_url"`
	TotalTimeTaken  float64 `json:"total_time_taken"`
}

// SearchParameters contains the parameters used for the search
type SearchParameters struct {
	Engine   string `json:"engine"`
	Query    string `json:"q"`
	Date     string `json:"date"`
	Timezone string `json:"tz"`
	DataType string `json:"data_type"`
	Geo      string `json:"geo,omitempty"`
	Language string `json:"hl,omitempty"`
}

// InterestOverTime represents time series data
type InterestOverTime struct {
	TimelineData []TimelineData `json:"timeline_data"`
	Averages     []QueryAverage `json:"averages"`
}

// TimelineData represents a single point in time
type TimelineData struct {
	Date      string       `json:"date"`
	Timestamp string       `json:"timestamp"`
	Values    []QueryValue `json:"values"`
}

// QueryValue represents the value for a specific query at a point in time
type QueryValue struct {
	Query          string `json:"query"`
	Value          string `json:"value"`
	ExtractedValue int    `json:"extracted_value"`
}

// QueryAverage represents the average value for a query
type QueryAverage struct {
	Query string `json:"query"`
	Value int    `json:"value"`
}

// RegionInterest represents interest data for a specific region
type RegionInterest struct {
	Geo            string `json:"geo"`
	Location       string `json:"location"`
	MaxValueIndex  int    `json:"max_value_index"`
	Value          string `json:"value"`
	ExtractedValue int    `json:"extracted_value"`
}

// ComparedRegion represents compared data across regions
type ComparedRegion struct {
	Geo           string       `json:"geo"`
	Location      string       `json:"location"`
	MaxValueIndex int          `json:"max_value_index"`
	Values        []QueryValue `json:"values"`
}

// RelatedTopics contains rising and top related topics
type RelatedTopics struct {
	Rising []TopicItem `json:"rising"`
	Top    []TopicItem `json:"top"`
}

// TopicItem represents a single related topic
type TopicItem struct {
	Topic          Topic  `json:"topic"`
	Value          string `json:"value"`
	ExtractedValue int    `json:"extracted_value"`
	Link           string `json:"link"`
	SerpApiLink    string `json:"serpapi_link"`
}

// Topic represents topic details
type Topic struct {
	Value string `json:"value"`
	Title string `json:"title"`
	Type  string `json:"type"`
}

// RelatedQueries contains rising and top related queries
type RelatedQueries struct {
	Rising []QueryItem `json:"rising"`
	Top    []QueryItem `json:"top"`
}

// QueryItem represents a single related query
type QueryItem struct {
	Query          string `json:"query"`
	Value          string `json:"value"`
	ExtractedValue int    `json:"extracted_value"`
	Link           string `json:"link"`
	SerpApiLink    string `json:"serpapi_link"`
}

// TrendsRequest represents a request for trends data
type TrendsRequest struct {
	Query                  string `json:"query" binding:"required"`
	DataType               string `json:"data_type,omitempty"`
	Geo                    string `json:"geo,omitempty"`
	Date                   string `json:"date,omitempty"`
	Language               string `json:"language,omitempty"`
	Category               string `json:"category,omitempty"`
	Property               string `json:"property,omitempty"`
	Timezone               string `json:"timezone,omitempty"`
	IncludeLowSearchVolume bool   `json:"include_low_search_volume,omitempty"`
}

// TrendsAnalysis represents processed trends analysis
type TrendsAnalysis struct {
	Query           string          `json:"query"`
	DataType        string          `json:"data_type"`
	GeneratedAt     time.Time       `json:"generated_at"`
	Summary         string          `json:"summary"`
	KeyInsights     []string        `json:"key_insights"`
	TrendDirection  string          `json:"trend_direction"`
	PeakPeriods     []string        `json:"peak_periods,omitempty"`
	TopRegions      []string        `json:"top_regions,omitempty"`
	RelatedKeywords []string        `json:"related_keywords,omitempty"`
	RawData         *TrendsResponse `json:"raw_data,omitempty"`
}

// TrendingNowResponse represents the response from Google Trends Trending Now API
type TrendingNowResponse struct {
	SearchMetadata   SearchMetadata           `json:"search_metadata"`
	SearchParameters TrendingSearchParameters `json:"search_parameters"`
	TrendingSearches []TrendingSearch         `json:"trending_searches"`
}

// TrendingSearchParameters contains the parameters used for trending search
type TrendingSearchParameters struct {
	Engine     string `json:"engine"`
	Geo        string `json:"geo"`
	Hours      int    `json:"hours,omitempty"`
	CategoryID string `json:"category_id,omitempty"`
	OnlyActive string `json:"only_active,omitempty"`
	Language   string `json:"hl,omitempty"`
}

// TrendingSearch represents a single trending search item
type TrendingSearch struct {
	Query              string             `json:"query"`
	StartTimestamp     int64              `json:"start_timestamp"`
	EndTimestamp       int64              `json:"end_timestamp,omitempty"`
	Active             bool               `json:"active"`
	SearchVolume       int                `json:"search_volume"`
	IncreasePercentage int                `json:"increase_percentage"`
	Categories         []TrendingCategory `json:"categories"`
	TrendBreakdown     []string           `json:"trend_breakdown,omitempty"`
	SerpApiTrendsLink  string             `json:"serpapi_google_trends_link"`
	NewsPageToken      string             `json:"news_page_token,omitempty"`
	SerpApiNewsLink    string             `json:"serpapi_news_link,omitempty"`
}

// TrendingCategory represents a category for trending search
type TrendingCategory struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}
