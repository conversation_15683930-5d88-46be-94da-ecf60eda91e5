package config

import (
	"fmt"
	"net/url"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// TaskGroup represents a group of scheduled tasks
type TaskGroup struct {
	Name     string
	Enabled  bool
	Geo      string
	Date     string
	DataType string
	Queries  []string

	// For EXPLORE mode
	Language        string
	Timezone        string
	Region          string
	Category        string
	ExploreDataType string // TIMESERIES, RELATED_TOPICS, RELATED_QUERIES, etc.

	// For TRENDING_NOW mode
	Hours      int
	CategoryID string

	// Scheduling options
	WorkdaysOnly   bool   // 是否只在工作日执行此任务组
	WorkdayPattern string // 工作日模式：MON-FRI, MON-SAT, 或自定义

	// For URL-style configuration
	OriginalURL string // 存储原始URL配置（不包含api_key）
}

// SchedulerConfig holds scheduler configuration
type SchedulerConfig struct {
	Enabled        bool
	Time           string
	Timezone       string
	WorkdaysOnly   bool   // 是否只在工作日执行
	WorkdayPattern string // 工作日模式：MON-FRI, MON-SAT, 或自定义
}

// WebInterfaceConfig holds web interface configuration
type WebInterfaceConfig struct {
	Enabled bool
	Path    string
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Enabled bool
	Path    string
}

// DataMaintenanceConfig holds data maintenance configuration
type DataMaintenanceConfig struct {
	Enabled        bool   // 是否启用数据维护
	RetentionDays  int    // 数据保留天数
	CleanupTime    string // 清理时间
	WorkdaysOnly   bool   // 是否只在工作日清理
	WorkdayPattern string // 清理频率模式
}

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port    string
	GinMode string

	// SerpApi configuration
	SerpApiKey string

	// API configuration
	MaxQueriesPerRequest int

	// Scheduler configuration
	Scheduler SchedulerConfig

	// Task groups configuration
	TaskGroupsEnabled bool
	TaskGroups        []TaskGroup

	// Web interface configuration
	WebInterface WebInterfaceConfig

	// Database configuration
	Database DatabaseConfig

	// Data maintenance configuration
	DataMaintenance DataMaintenanceConfig
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		// .env file is optional, so we don't return error
		fmt.Println("No .env file found, using environment variables")
	}

	config := &Config{
		Port:    getEnv("PORT", "8080"),
		GinMode: getEnv("GIN_MODE", "debug"),

		SerpApiKey: getEnv("SERPAPI_KEY", ""),

		MaxQueriesPerRequest: getEnvAsInt("MAX_QUERIES_PER_REQUEST", 5),

		// Scheduler configuration
		Scheduler: SchedulerConfig{
			Enabled:        getEnvAsBool("SCHEDULER_ENABLED", false),
			Time:           getEnv("SCHEDULER_TIME", "09:01"),
			Timezone:       getEnv("SCHEDULER_TIMEZONE", "Asia/Shanghai"),
			WorkdaysOnly:   getEnvAsBool("SCHEDULER_WORKDAYS_ONLY", false),
			WorkdayPattern: getEnv("SCHEDULER_WORKDAY_PATTERN", "MON-FRI"),
		},

		// Task groups configuration
		TaskGroupsEnabled: getEnvAsBool("TASK_GROUPS_ENABLED", false),

		// Web interface configuration
		WebInterface: WebInterfaceConfig{
			Enabled: getEnvAsBool("WEB_INTERFACE_ENABLED", true),
			Path:    getEnv("WEB_INTERFACE_PATH", "/admin"),
		},

		// Database configuration
		Database: DatabaseConfig{
			Enabled: getEnvAsBool("DATABASE_ENABLED", true),
			Path:    getEnv("DATABASE_PATH", "./data/trends.db"),
		},

		// Data maintenance configuration
		DataMaintenance: DataMaintenanceConfig{
			Enabled:        getEnvAsBool("DATA_MAINTENANCE_ENABLED", true),
			RetentionDays:  getEnvAsInt("DATA_RETENTION_DAYS", 5),
			CleanupTime:    getEnv("DATA_CLEANUP_TIME", "02:00"),
			WorkdaysOnly:   getEnvAsBool("DATA_CLEANUP_WORKDAYS_ONLY", false),
			WorkdayPattern: getEnv("DATA_CLEANUP_WORKDAY_PATTERN", "DAILY"),
		},
	}

	// Load task groups
	config.TaskGroups = loadTaskGroups()

	// Validate required configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.SerpApiKey == "" {
		fmt.Println("Info: SERPAPI_KEY is not set. Using API keys from task URLs directly.")
	}

	// Validate max queries
	if c.MaxQueriesPerRequest < 1 || c.MaxQueriesPerRequest > 5 {
		return fmt.Errorf("MAX_QUERIES_PER_REQUEST must be between 1 and 5")
	}

	return nil
}

// GetServerAddress returns the server address
func (c *Config) GetServerAddress() string {
	return ":" + c.Port
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return fallback
}

// getEnvAsBool gets an environment variable as boolean with a fallback value
func getEnvAsBool(key string, fallback bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return fallback
}

// loadTaskGroups dynamically loads task groups from environment variables
// Supports both new URL-style format (TASK_alias=params) and legacy format (TASK_GROUP_N_*)
func loadTaskGroups() []TaskGroup {
	var taskGroups []TaskGroup

	// First, try to load new URL-style task configurations
	urlStyleTasks := loadURLStyleTaskGroups()
	taskGroups = append(taskGroups, urlStyleTasks...)

	// Then, load legacy format for backward compatibility
	legacyTasks := loadLegacyTaskGroups()
	taskGroups = append(taskGroups, legacyTasks...)

	return taskGroups
}

// loadURLStyleTaskGroups loads task groups from new URL-style configuration
// Format: 任务名称=完整的SerpAPI请求URL
func loadURLStyleTaskGroups() []TaskGroup {
	var taskGroups []TaskGroup

	fmt.Println("🔍 Loading URL-style task groups...")

	// Get all environment variables
	for _, env := range os.Environ() {
		pair := strings.SplitN(env, "=", 2)
		if len(pair) != 2 {
			continue
		}

		key := pair[0]
		value := pair[1]

		// Skip standard config variables and legacy TASK_GROUP_ variables
		if isStandardConfigVar(key) || strings.HasPrefix(key, "TASK_GROUP_") {
			continue
		}

		// Check if this looks like a SerpAPI URL
		if strings.Contains(value, "serpapi.com/search.json") {
			fmt.Printf("📋 Found task: %s\n", key)
			// Use the key as task name directly
			taskGroup := parseDirectURLTaskGroup(key, value)
			if taskGroup != nil {
				taskGroups = append(taskGroups, *taskGroup)
				fmt.Printf("✅ Loaded task: %s\n", taskGroup.Name)
			}
		}
	}

	fmt.Printf("📊 Total URL-style tasks loaded: %d\n", len(taskGroups))
	return taskGroups
}

// isStandardConfigVar checks if a key is a standard configuration variable
func isStandardConfigVar(key string) bool {
	standardVars := []string{
		"SERPAPI_KEY", "PORT", "GIN_MODE", "MAX_QUERIES_PER_REQUEST",
		"SCHEDULER_ENABLED", "SCHEDULER_TIME", "SCHEDULER_TIMEZONE",
		"SCHEDULER_WORKDAYS_ONLY", "SCHEDULER_WORKDAY_PATTERN", "TASK_GROUPS_ENABLED",
		"WEB_INTERFACE_ENABLED", "WEB_INTERFACE_PATH", "DATABASE_ENABLED", "DATABASE_PATH",
		"DATA_MAINTENANCE_ENABLED", "DATA_RETENTION_DAYS", "DATA_CLEANUP_TIME",
		"DATA_CLEANUP_WORKDAYS_ONLY", "DATA_CLEANUP_WORKDAY_PATTERN",
	}

	for _, stdVar := range standardVars {
		if key == stdVar {
			return true
		}
	}
	return false
}

// loadLegacyTaskGroups loads task groups using legacy format for backward compatibility
func loadLegacyTaskGroups() []TaskGroup {
	var taskGroups []TaskGroup

	// Dynamically detect task groups by checking environment variables
	// We'll check for TASK_GROUP_N_ENABLED where N starts from 1
	for i := 1; i <= 100; i++ { // Check up to 100 task groups (reasonable limit)
		enabledKey := fmt.Sprintf("TASK_GROUP_%d_ENABLED", i)

		// If this task group is not enabled, skip it
		if !getEnvAsBool(enabledKey, false) {
			continue
		}

		// Load task group configuration
		taskGroup := loadSingleTaskGroup(i)
		if taskGroup != nil {
			taskGroups = append(taskGroups, *taskGroup)
		}
	}

	return taskGroups
}

// parseDirectURLTaskGroup parses a direct SerpAPI URL configuration
// Format: 任务名称=完整的SerpAPI请求URL(包含api_key)
func parseDirectURLTaskGroup(taskName, urlString string) *TaskGroup {
	// Parse the complete URL to extract parameters
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		fmt.Printf("Warning: Failed to parse task URL '%s': %v\n", taskName, err)
		return nil
	}

	// Extract query parameters
	query := parsedURL.Query()

	// Determine data type and engine from URL
	engine := query.Get("engine")
	dataType := query.Get("data_type")
	if dataType == "" {
		// Determine based on engine
		switch engine {
		case "google_trends_trending_now":
			dataType = "TRENDING_NOW"
		case "google_trends":
			dataType = "TIMESERIES" // Default for google_trends
		default:
			dataType = "TIMESERIES" // Default fallback
		}
	}

	// Create task group with parsed parameters
	taskGroup := &TaskGroup{
		Name:            taskName, // Use the key as task name directly
		Enabled:         true,     // Default enabled for direct URL format
		Geo:             query.Get("geo"),
		Date:            getQueryParam(query, "date", "now 7-d"),
		DataType:        dataType,
		Language:        query.Get("hl"),
		Timezone:        query.Get("tz"),
		Region:          query.Get("region"),
		Category:        query.Get("cat"),
		Hours:           getQueryParamAsInt(query, "hours", 0),
		CategoryID:      query.Get("category_id"),
		ExploreDataType: query.Get("data_type"),
		WorkdaysOnly:    false,     // Default for direct URL format
		WorkdayPattern:  "MON-FRI", // Default pattern
	}

	// Parse queries if provided
	if queryStr := query.Get("q"); queryStr != "" {
		taskGroup.Queries = parseQueries(queryStr)
	}

	// Store the original URL for direct API calls
	taskGroup.OriginalURL = urlString

	return taskGroup
}

// Helper functions for URL query parameters
func getQueryParam(query url.Values, key, defaultValue string) string {
	if value := query.Get(key); value != "" {
		return value
	}
	return defaultValue
}

func getQueryParamAsInt(query url.Values, key string, defaultValue int) int {
	if value := query.Get(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// parseCompleteURL parses a complete SerpAPI URL and extracts all parameters
func parseCompleteURL(urlString string) (map[string]string, error) {
	params := make(map[string]string)

	// Parse the URL
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return nil, fmt.Errorf("invalid URL format: %w", err)
	}

	// Extract query parameters
	queryParams := parsedURL.Query()
	for key, values := range queryParams {
		if len(values) > 0 {
			params[key] = values[0] // Take the first value if multiple exist
		}
	}

	return params, nil
}

// determineDataType determines the appropriate DataType based on engine and parameters
func determineDataType(engine string, params map[string]string) string {
	// If data_type is explicitly specified, use it
	if dataType := getStringParam(params, "data_type", ""); dataType != "" {
		return dataType
	}

	// Determine based on engine
	switch engine {
	case "google_trends_trending_now":
		return "TRENDING_NOW"
	case "google_trends":
		// For google_trends, check if there's a query parameter
		if getStringParam(params, "q", "") != "" {
			return "TIMESERIES" // Default for queries
		}
		return "EXPLORE" // Default for no queries
	default:
		return "TIMESERIES" // Default fallback
	}
}

// removeAPIKeyFromURL removes api_key parameter from URL if present
func removeAPIKeyFromURL(urlString string) string {
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return urlString // Return original if parsing fails
	}

	// Remove api_key parameter
	query := parsedURL.Query()
	query.Del("api_key")
	parsedURL.RawQuery = query.Encode()

	return parsedURL.String()
}

// Helper functions to get typed parameters from map
func getStringParam(params map[string]string, key, defaultValue string) string {
	if value, exists := params[key]; exists {
		return value
	}
	return defaultValue
}

func getIntParam(params map[string]string, key string, defaultValue int) int {
	if value, exists := params[key]; exists {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getBoolParam(params map[string]string, key string, defaultValue bool) bool {
	if value, exists := params[key]; exists {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// loadSingleTaskGroup loads a single task group configuration by index
func loadSingleTaskGroup(index int) *TaskGroup {
	// Build environment variable keys for this task group
	nameKey := fmt.Sprintf("TASK_GROUP_%d_NAME", index)
	geoKey := fmt.Sprintf("TASK_GROUP_%d_GEO", index)
	dateKey := fmt.Sprintf("TASK_GROUP_%d_DATE", index)
	dataTypeKey := fmt.Sprintf("TASK_GROUP_%d_DATA_TYPE", index)
	queriesKey := fmt.Sprintf("TASK_GROUP_%d_QUERIES", index)
	languageKey := fmt.Sprintf("TASK_GROUP_%d_LANGUAGE", index)
	timezoneKey := fmt.Sprintf("TASK_GROUP_%d_TIMEZONE", index)
	regionKey := fmt.Sprintf("TASK_GROUP_%d_REGION", index)
	categoryKey := fmt.Sprintf("TASK_GROUP_%d_CATEGORY", index)
	hoursKey := fmt.Sprintf("TASK_GROUP_%d_HOURS", index)
	categoryIDKey := fmt.Sprintf("TASK_GROUP_%d_CATEGORY_ID", index)
	exploreDataTypeKey := fmt.Sprintf("TASK_GROUP_%d_EXPLORE_DATA_TYPE", index)
	workdaysOnlyKey := fmt.Sprintf("TASK_GROUP_%d_WORKDAYS_ONLY", index)
	workdayPatternKey := fmt.Sprintf("TASK_GROUP_%d_WORKDAY_PATTERN", index)

	// Load configuration values
	dataType := getEnv(dataTypeKey, "TIMESERIES")
	queries := parseQueries(getEnv(queriesKey, ""))

	// For EXPLORE and TRENDING_NOW modes, queries are optional
	// For TIMESERIES mode, queries are required
	if dataType != "EXPLORE" && dataType != "TRENDING_NOW" && len(queries) == 0 {
		// Skip this task group if it's TIMESERIES mode but has no queries
		return nil
	}

	// Create task group
	taskGroup := &TaskGroup{
		Name:            getEnv(nameKey, fmt.Sprintf("TaskGroup%d", index)),
		Enabled:         true, // Already checked that it's enabled
		Geo:             getEnv(geoKey, ""),
		Date:            getEnv(dateKey, "now 7-d"),
		DataType:        dataType,
		Queries:         queries,
		Language:        getEnv(languageKey, ""),
		Timezone:        getEnv(timezoneKey, ""),
		Region:          getEnv(regionKey, ""),
		Category:        getEnv(categoryKey, ""),
		Hours:           getEnvAsInt(hoursKey, 0),
		CategoryID:      getEnv(categoryIDKey, ""),
		ExploreDataType: getEnv(exploreDataTypeKey, ""),
		WorkdaysOnly:    getEnvAsBool(workdaysOnlyKey, false),
		WorkdayPattern:  getEnv(workdayPatternKey, "MON-FRI"),
	}

	return taskGroup
}

// parseQueries parses comma-separated queries string
func parseQueries(queriesStr string) []string {
	if queriesStr == "" {
		return nil
	}

	var queries []string
	for _, query := range strings.Split(queriesStr, ",") {
		query = strings.TrimSpace(query)
		if query != "" {
			queries = append(queries, query)
		}
	}
	return queries
}
