package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// DingTalkConfig holds DingTalk configuration
type DingTalkConfig struct {
	// 企业应用基本信息
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	AgentID      string `json:"agent_id"`
	AppID        string `json:"app_id"`

	// 机器人配置
	BotName        string `json:"bot_name"`
	BotDescription string `json:"bot_description"`
	BotEnabled     bool   `json:"bot_enabled"`

	// 推送配置
	PushEnabled bool `json:"push_enabled"`

	// 用户和群组配置
	Users            string `json:"users"`
	Groups           string `json:"groups"`
	DefaultTestUser  string `json:"default_test_user"`
	DefaultTestGroup string `json:"default_test_group"`

	// API配置
	APIBaseURL     string `json:"api_base_url"`
	TimeoutSeconds int    `json:"timeout_seconds"`

	// 消息格式配置
	MessageTitlePrefix   string `json:"message_title_prefix"`
	MessageMaxLength     int    `json:"message_max_length"`
	MessageIncludeCharts bool   `json:"message_include_charts"`
	MessageIncludeLinks  bool   `json:"message_include_links"`

	// 错误处理配置
	RetryAttempts     int    `json:"retry_attempts"`
	RetryDelaySeconds int    `json:"retry_delay_seconds"`
	LogLevel          string `json:"log_level"`
}

// LoadDingTalkConfig loads DingTalk configuration from .env.dingtalk file
func LoadDingTalkConfig() (*DingTalkConfig, error) {
	// Load .env.dingtalk file
	err := godotenv.Load(".env.dingtalk")
	if err != nil {
		return nil, err
	}

	config := &DingTalkConfig{
		// 企业应用基本信息
		ClientID:     getDingTalkEnv("DINGTALK_CLIENT_ID", ""),
		ClientSecret: getDingTalkEnv("DINGTALK_CLIENT_SECRET", ""),
		AgentID:      getDingTalkEnv("DINGTALK_AGENT_ID", ""),
		AppID:        getDingTalkEnv("DINGTALK_APP_ID", ""),

		// 机器人配置
		BotName:        getDingTalkEnv("DINGTALK_BOT_NAME", "趋势小助手"),
		BotDescription: getDingTalkEnv("DINGTALK_BOT_DESCRIPTION", "🤖 我是趋势小助手，服务于lily"),
		BotEnabled:     getDingTalkBoolEnv("DINGTALK_BOT_ENABLED", true),

		// 推送配置
		PushEnabled: getDingTalkBoolEnv("DINGTALK_PUSH_ENABLED", true),

		// 用户和群组配置
		Users:            getDingTalkEnv("DINGTALK_USERS", ""),
		Groups:           getDingTalkEnv("DINGTALK_GROUPS", ""),
		DefaultTestUser:  getDingTalkEnv("DINGTALK_DEFAULT_TEST_USER", "024326423122739246"),
		DefaultTestGroup: getDingTalkEnv("DINGTALK_DEFAULT_TEST_GROUP", "cidC1O/vEbs9hupD0d7yf2DWg=="),

		// API配置
		APIBaseURL:     getDingTalkEnv("DINGTALK_API_BASE_URL", "https://oapi.dingtalk.com"),
		TimeoutSeconds: getDingTalkIntEnv("DINGTALK_TIMEOUT_SECONDS", 30),

		// 消息格式配置
		MessageTitlePrefix:   getDingTalkEnv("DINGTALK_MESSAGE_TITLE_PREFIX", "📊 Google Trends"),
		MessageMaxLength:     getDingTalkIntEnv("DINGTALK_MESSAGE_MAX_LENGTH", 4096),
		MessageIncludeCharts: getDingTalkBoolEnv("DINGTALK_MESSAGE_INCLUDE_CHARTS", true),
		MessageIncludeLinks:  getDingTalkBoolEnv("DINGTALK_MESSAGE_INCLUDE_LINKS", true),

		// 错误处理配置
		RetryAttempts:     getDingTalkIntEnv("DINGTALK_RETRY_ATTEMPTS", 3),
		RetryDelaySeconds: getDingTalkIntEnv("DINGTALK_RETRY_DELAY_SECONDS", 5),
		LogLevel:          getDingTalkEnv("DINGTALK_LOG_LEVEL", "INFO"),
	}

	return config, nil
}

// Helper functions for environment variable parsing

func getDingTalkBoolEnv(key string, defaultValue bool) bool {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	result, err := strconv.ParseBool(value)
	if err != nil {
		return defaultValue
	}
	return result
}

func getDingTalkIntEnv(key string, defaultValue int) int {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	result, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}
	return result
}

func getDingTalkEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
