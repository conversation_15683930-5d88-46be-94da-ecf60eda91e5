# 钉钉企业应用配置
# DingTalk Enterprise Application Configuration

# 企业应用基本信息
DINGTALK_CLIENT_ID=dingxnsub6gs6moigzfs
DINGTALK_CLIENT_SECRET=NCNuAx9dofID1KFQnGOKt2lK9l0RqHgB_LD7MI9hCoL5rBb5EFgHU8xJkLLVyHG-
DINGTALK_AGENT_ID=3933874685
DINGTALK_APP_ID=c4177d9a-8129-4486-aa1e-9cf08c2bb027

# 机器人配置
DINGTALK_BOT_NAME=趋势小助手
DINGTALK_BOT_DESCRIPTION=🤖 我是趋势助手，阁下请多关照
DINGTALK_BOT_ENABLED=true

# 推送配置
DINGTALK_PUSH_ENABLED=true

# 钉钉用户和群组配置
# 用户配置: userID:userName,userID:userName
DINGTALK_USERS=22684358591104782:xiaoYuan（袁可）,024326423122739246:jh

# 群组配置: groupID:groupName|groupID:groupName (使用|分隔避免逗号冲突)
DINGTALK_GROUPS=cidC1O/vEbs9hupD0d7yf2DWg==:塔塔罗特|cid7notLFWYHlJlxDwYAogb2Q==:JH(姜昊),xiaoYuan（袁可）



# 自动ID收集功能
# 当用户给机器人发送消息时，自动收集用户ID和群组ID
DINGTALK_AUTO_COLLECT_IDS=true
DINGTALK_ID_CONFIG_FILE=dingtalk_ids.txt

# API配置
DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
DINGTALK_TIMEOUT_SECONDS=30

# 消息格式配置
DINGTALK_MESSAGE_TITLE_PREFIX=📊 Google Trends
DINGTALK_MESSAGE_MAX_LENGTH=4096
DINGTALK_MESSAGE_INCLUDE_CHARTS=true
DINGTALK_MESSAGE_INCLUDE_LINKS=true

# 错误处理配置
DINGTALK_RETRY_ATTEMPTS=3
DINGTALK_RETRY_DELAY_SECONDS=5
DINGTALK_LOG_LEVEL=INFO
